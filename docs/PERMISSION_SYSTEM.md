# DNS Bot 权限管理系统

本文档详细说明DNS Bot的权限管理系统，包括角色定义、命令权限映射和管理方式。

## 🎯 权限级别

### 1. 普通用户 (user)
- **权限**: 仅基本命令
- **可用命令**: `/start`, `/help`
- **特点**: 发送其他命令时静默拒绝（无响应）

### 2. 管理员 (admin)
- **权限**: DNS管理 + 过期管理
- **可用命令**: 所有DNS和过期管理命令
- **特点**: 可以管理DNS记录和过期设置

### 3. 超级管理员 (super_admin)
- **权限**: 所有权限
- **可用命令**: 所有命令 + 用户管理
- **特点**: 可以修改用户角色和系统设置

## 📋 命令权限映射

### 基础命令 (所有用户)
| 命令 | 权限要求 | 描述 |
|------|----------|------|
| `/start` | user | 开始使用Bot |
| `/help` | user | 显示可用命令 |

### DNS管理命令 (管理员+)
| 命令 | 权限要求 | 描述 | 用法 |
|------|----------|------|------|
| `/add` | admin | 添加DNS记录 | `/add [name] [type] [content] [options]` |
| `/list` | admin | 显示DNS记录 | `/list` |
| `/delete` | admin | 删除DNS记录 | `/delete [id]` |
| `/deleteall` | admin | 删除所有DNS记录 | `/deleteall` |

### 过期管理命令 (管理员+)
| 命令 | 权限要求 | 描述 | 用法 |
|------|----------|------|------|
| `/renew` | admin | 续期DNS过期时间 | `/renew` |
| `/expiry` | admin | 查看过期状态 | `/expiry` |
| `/reminder` | admin | 管理过期提醒 | `/reminder [action]` |
| `/status` | admin | 显示Bot状态 | `/status` |

### 系统管理命令 (超级管理员)
| 命令 | 权限要求 | 描述 | 用法 |
|------|----------|------|------|
| `/users` | super_admin | 列出所有用户及其角色 | `/users` |
| `/setrole` | super_admin | 设置用户角色 | `/setrole [user_id] [role]` |
| `/clearwebhook` | super_admin | 清除webhook设置 | `/clearwebhook` |

## 🔧 技术实现

### 权限配置结构
```typescript
interface CommandConfig {
  command: string;           // 命令名称
  requiredRole: UserRole;    // 所需权限级别
  description: string;       // 命令描述
  usage?: string;           // 使用方法
  category: string;         // 命令分类
  options?: string[];       // 可选参数说明
}
```

### 权限检查流程
1. **命令接收**: 用户发送命令
2. **角色获取**: 从数据库查询用户角色
3. **权限验证**: 检查用户是否有权限执行该命令
4. **静默拒绝**: 无权限时不返回任何响应
5. **命令执行**: 有权限时执行相应功能

### 核心函数
```typescript
// 检查命令权限
function hasCommandPermission(userRole: UserRole, command: string): boolean

// 获取可用命令列表
function getAvailableCommands(userRole: UserRole): CommandConfig[]

// 获取用户角色
async function getUserRole(c: Context, userId: number): Promise<UserRole>
```

## 🛡️ 安全特性

### 1. 静默拒绝
- 普通用户发送管理员命令时不会收到任何响应
- 避免暴露系统功能给未授权用户
- 提供更好的安全性

### 2. 角色保护
- 用户信息更新时不会重置角色
- 只有超级管理员可以修改用户角色
- 角色变更有完整的审计日志

### 3. 分级权限
- 三级权限系统，层次清晰
- 高级权限包含低级权限
- 最小权限原则

## 📊 权限管理

### 查看用户列表
```bash
# 查看所有用户及其角色
/users
```

### 设置用户角色
```bash
# 首先查看用户列表获取用户ID
/users

# 设置为管理员
/setrole 123456789 admin

# 设置为普通用户
/setrole 123456789 user

# 设置为超级管理员
/setrole 123456789 super_admin
```

### 查看权限
```bash
# 查看自己的可用命令
/help
```

### 权限验证
- 每个命令执行前都会验证权限
- 权限不足时静默拒绝
- 所有权限操作都有审计日志

## 🔍 故障排除

### 常见问题

1. **命令无响应**
   - 检查用户角色是否正确
   - 确认命令拼写正确
   - 验证权限配置

2. **角色设置失败**
   - 确认操作者是超级管理员
   - 检查目标用户ID是否正确
   - 验证角色名称拼写

3. **权限被重置**
   - 现已修复，用户信息更新不会影响角色
   - 如有问题，联系超级管理员重新设置

### 调试命令
```bash
# 查看当前权限
/help

# 测试权限
/status  # 管理员命令测试

# 设置角色（仅超级管理员）
/setrole [user_id] [role]
```

## 📈 权限统计

### 命令分布
- **基础命令**: 2个 (所有用户)
- **DNS管理**: 4个 (管理员+)
- **过期管理**: 4个 (管理员+)
- **系统管理**: 3个 (超级管理员)

### 角色分布
- **普通用户**: 2个可用命令
- **管理员**: 10个可用命令
- **超级管理员**: 13个可用命令

## 🚀 最佳实践

### 对于超级管理员
1. **谨慎授权**: 只给信任的用户管理员权限
2. **定期审查**: 定期检查用户角色分配
3. **备份管理**: 设置多个超级管理员防止意外
4. **监控日志**: 关注权限变更的审计日志

### 对于管理员
1. **了解权限**: 熟悉自己可用的命令
2. **安全操作**: 谨慎使用删除类命令
3. **及时续期**: 定期检查和续期DNS过期时间

### 对于普通用户
1. **联系管理员**: 需要权限时联系管理员
2. **了解限制**: 理解自己的权限范围

## 🔮 未来增强

### 计划功能
1. **细粒度权限**: 更详细的权限控制
2. **临时权限**: 时间限制的权限授予
3. **权限组**: 批量权限管理
4. **权限审计**: 更详细的权限使用统计

### API扩展
1. **权限查询API**: 查询用户权限
2. **批量角色设置**: 批量修改用户角色
3. **权限报告**: 生成权限使用报告

## 📝 总结

新的权限管理系统提供了：
- ✅ **集中配置**: 所有权限在一个地方管理
- ✅ **类型安全**: TypeScript类型检查
- ✅ **易于维护**: 清晰的权限映射
- ✅ **安全可靠**: 静默拒绝和角色保护
- ✅ **可扩展**: 易于添加新命令和权限

这个系统确保了DNS Bot的安全性和可管理性，同时提供了良好的用户体验。
