-- DNS Bot Database Schema (No Foreign Keys)
-- This schema removes all foreign key constraints for better compatibility

-- Users table for storing Telegram user information
CREATE TABLE IF NOT EXISTS users_new (
  id INTEGER PRIMARY KEY,
  chat_id INTEGER UNIQUE NOT NULL,
  role TEXT NOT NULL DEFAULT 'user',
  first_name TEXT,
  last_name TEXT,
  username TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_active DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Audit log table for tracking all actions
CREATE TABLE IF NOT EXISTS audit_log_new (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  action TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id TEXT,
  details TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Bot settings table for configuration
CREATE TABLE IF NOT EXISTS bot_settings_new (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- DNS backups table for storing backup metadata (no foreign keys)
CREATE TABLE IF NOT EXISTS dns_backups_new (
  backup_id TEXT PRIMARY KEY,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by INTEGER NOT NULL,
  reason TEXT NOT NULL,
  record_count INTEGER NOT NULL DEFAULT 0,
  zone_id TEXT NOT NULL,
  zone_name TEXT NOT NULL,
  backup_type TEXT NOT NULL DEFAULT 'manual', -- 'manual', 'auto_cleanup', 'auto_expiry'
  metadata TEXT -- JSON metadata
);

-- DNS backup records table for storing individual DNS records (no foreign keys)
CREATE TABLE IF NOT EXISTS dns_backup_records_new (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  backup_id TEXT NOT NULL,
  record_id TEXT NOT NULL, -- Original Cloudflare record ID
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  content TEXT NOT NULL,
  proxied INTEGER NOT NULL DEFAULT 0,
  ttl INTEGER NOT NULL DEFAULT 1,
  zone_id TEXT NOT NULL,
  zone_name TEXT NOT NULL,
  created_on DATETIME,
  modified_on DATETIME,
  record_data TEXT -- Full JSON record data
);

-- Copy data from old tables to new tables
INSERT OR IGNORE INTO users_new SELECT * FROM users;
INSERT OR IGNORE INTO audit_log_new SELECT * FROM audit_log;
INSERT OR IGNORE INTO bot_settings_new SELECT * FROM bot_settings;

-- Drop old tables
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS audit_log;
DROP TABLE IF EXISTS bot_settings;
DROP TABLE IF EXISTS dns_backups;
DROP TABLE IF EXISTS dns_backup_records;

-- Rename new tables to original names
ALTER TABLE users_new RENAME TO users;
ALTER TABLE audit_log_new RENAME TO audit_log;
ALTER TABLE bot_settings_new RENAME TO bot_settings;
ALTER TABLE dns_backups_new RENAME TO dns_backups;
ALTER TABLE dns_backup_records_new RENAME TO dns_backup_records;

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_users_chat_id ON users(chat_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_dns_backups_created_at ON dns_backups(created_at);
CREATE INDEX IF NOT EXISTS idx_dns_backups_created_by ON dns_backups(created_by);
CREATE INDEX IF NOT EXISTS idx_dns_backups_type ON dns_backups(backup_type);
CREATE INDEX IF NOT EXISTS idx_dns_backup_records_backup_id ON dns_backup_records(backup_id);
CREATE INDEX IF NOT EXISTS idx_dns_backup_records_name ON dns_backup_records(name);
CREATE INDEX IF NOT EXISTS idx_dns_backup_records_type ON dns_backup_records(type);
