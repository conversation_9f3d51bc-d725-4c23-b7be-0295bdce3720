{"name": "dns-bot", "version": "1.0.0", "description": "Telegram Bot for Cloudflare DNS management with OpenAPI", "main": "src/index.js", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "build": "npm run clean && tsc --noEmitOnError false", "build:quick": "npm run clean && tsc --noCheck", "clean": "rm -rf dist", "test": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage", "lint": "eslint src/ --ext .ts", "lint:fix": "eslint src/ --ext .ts --fix", "format": "prettier --write src/", "format:check": "prettier --check src/", "type-check": "tsc --noEmit"}, "keywords": ["cloudflare", "worker", "telegram", "bot", "dns", "openapi"], "author": "", "license": "MIT", "devDependencies": {"@cloudflare/workers-types": "^4.20231218.0", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.55.0", "prettier": "^3.1.0", "typescript": "^5.3.0", "vitest": "^1.0.0", "wrangler": "^3.19.0"}, "dependencies": {"@hono/swagger-ui": "^0.2.2", "@hono/zod-openapi": "^0.8.0", "hono": "^3.11.0", "zod": "^3.22.0"}}