-- DNS Bot Database Schema
-- Note: DNS records are now managed directly through Cloudflare API
-- No local storage of DNS records needed

-- Users table for tracking bot users
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY,
    username TEXT,
    first_name TEXT,
    last_name TEXT,
    chat_id INTEGER,
    is_admin BOOLEAN DEFAULT FALSE,
    role TEXT DEFAULT 'user',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_active DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Bot settings table
CREATE TABLE IF NOT EXISTS bot_settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Audit log for tracking operations
CREATE TABLE IF NOT EXISTS audit_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action TEXT NOT NULL,
    resource_type TEXT,
    resource_id TEXT,
    details TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREI<PERSON><PERSON> KEY (user_id) REFERENCES users (id)
);

-- DNS backups table for storing backup metadata
CREATE TABLE IF NOT EXISTS dns_backups (
  backup_id TEXT PRIMARY KEY,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by INTEGER NOT NULL,
  reason TEXT NOT NULL,
  record_count INTEGER NOT NULL DEFAULT 0,
  zone_id TEXT NOT NULL,
  zone_name TEXT NOT NULL,
  backup_type TEXT NOT NULL DEFAULT 'manual', -- 'manual', 'auto_cleanup', 'auto_expiry'
  metadata TEXT, -- JSON metadata
  FOREIGN KEY (created_by) REFERENCES users(id)
);

-- DNS backup records table for storing individual DNS records
CREATE TABLE IF NOT EXISTS dns_backup_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  backup_id TEXT NOT NULL,
  record_id TEXT NOT NULL, -- Original Cloudflare record ID
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  content TEXT NOT NULL,
  proxied INTEGER NOT NULL DEFAULT 0,
  ttl INTEGER NOT NULL DEFAULT 1,
  zone_id TEXT NOT NULL,
  zone_name TEXT NOT NULL,
  created_on DATETIME,
  modified_on DATETIME,
  record_data TEXT, -- Full JSON record data
  FOREIGN KEY (backup_id) REFERENCES dns_backups(backup_id) ON DELETE CASCADE
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_users_chat_id ON users(chat_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_dns_backups_created_at ON dns_backups(created_at);
CREATE INDEX IF NOT EXISTS idx_dns_backups_created_by ON dns_backups(created_by);
CREATE INDEX IF NOT EXISTS idx_dns_backups_type ON dns_backups(backup_type);
CREATE INDEX IF NOT EXISTS idx_dns_backup_records_backup_id ON dns_backup_records(backup_id);
CREATE INDEX IF NOT EXISTS idx_dns_backup_records_name ON dns_backup_records(name);
CREATE INDEX IF NOT EXISTS idx_dns_backup_records_type ON dns_backup_records(type);

-- Insert default settings
INSERT OR IGNORE INTO bot_settings (key, value) VALUES
    ('webhook_url', ''),
    ('max_records_per_user', '10'),
    ('default_ttl', '300'),
    ('cleanup_enabled', 'true'),
    ('dns_expiry_time', datetime('now', '+24 hours')),
    ('reminder_enabled', 'true'),
    ('last_reminder_time', '');
