-- DNS Bot Database Schema
-- Note: DNS records are now managed directly through Cloudflare API
-- No local storage of DNS records needed

-- Users table for tracking bot users
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY,
    username TEXT,
    first_name TEXT,
    last_name TEXT,
    chat_id INTEGER,
    is_admin BOOLEAN DEFAULT FALSE,
    role TEXT DEFAULT 'user',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_active DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Bot settings table
CREATE TABLE IF NOT EXISTS bot_settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Audit log for tracking operations
CREATE TABLE IF NOT EXISTS audit_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action TEXT NOT NULL,
    resource_type TEXT,
    resource_id TEXT,
    details TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREI<PERSON><PERSON> KEY (user_id) REFERENCES users (id)
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_users_chat_id ON users(chat_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- Insert default settings
INSERT OR IGNORE INTO bot_settings (key, value) VALUES
    ('webhook_url', ''),
    ('max_records_per_user', '10'),
    ('default_ttl', '300'),
    ('cleanup_enabled', 'true'),
    ('dns_expiry_time', datetime('now', '+24 hours')),
    ('reminder_enabled', 'true'),
    ('last_reminder_time', '');
