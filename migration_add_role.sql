-- Migration: Add role column to users table
-- This migration adds the role column for the new permission system

-- Add role column to users table
ALTER TABLE users ADD COLUMN role TEXT DEFAULT 'user';

-- Update existing admin users to have admin role
UPDATE users SET role = 'admin' WHERE is_admin = TRUE;

-- Create index for better performance on role lookups
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
