// 本地化配置
export const LOCALE_CONFIG = {
  // 时区配置
  TIMEZONE: 'Asia/Shanghai', // +8时区
  TIMEZONE_OFFSET: 8, // UTC+8
  
  // 语言配置
  LANGUAGE: 'zh-CN',
  
  // 日期时间格式
  DATE_FORMAT: {
    SHORT: 'YYYY-MM-DD',
    LONG: 'YYYY-MM-DD HH:mm:ss',
    TIME_ONLY: 'HH:mm:ss'
  }
};

// 时间工具函数
export class TimeUtils {
  /**
   * 获取当前北京时间
   */
  static now(): Date {
    const utcTime = new Date();
    const beijingTime = new Date(utcTime.getTime() + (LOCALE_CONFIG.TIMEZONE_OFFSET * 60 * 60 * 1000));
    return beijingTime;
  }

  /**
   * 将UTC时间转换为北京时间
   */
  static utcToBeijing(utcDate: Date | string): Date {
    const date = typeof utcDate === 'string' ? new Date(utcDate) : utcDate;
    return new Date(date.getTime() + (LOCALE_CONFIG.TIMEZONE_OFFSET * 60 * 60 * 1000));
  }

  /**
   * 将北京时间转换为UTC时间
   */
  static beijingToUtc(beijingDate: Date): Date {
    return new Date(beijingDate.getTime() - (LOCALE_CONFIG.TIMEZONE_OFFSET * 60 * 60 * 1000));
  }

  /**
   * 格式化时间显示（北京时间）
   */
  static formatBeijingTime(date: Date | string, format: 'short' | 'long' | 'time' = 'long'): string {
    const beijingTime = typeof date === 'string' ? this.utcToBeijing(new Date(date)) : this.utcToBeijing(date);
    
    const year = beijingTime.getFullYear();
    const month = String(beijingTime.getMonth() + 1).padStart(2, '0');
    const day = String(beijingTime.getDate()).padStart(2, '0');
    const hours = String(beijingTime.getHours()).padStart(2, '0');
    const minutes = String(beijingTime.getMinutes()).padStart(2, '0');
    const seconds = String(beijingTime.getSeconds()).padStart(2, '0');

    switch (format) {
      case 'short':
        return `${year}-${month}-${day}`;
      case 'time':
        return `${hours}:${minutes}:${seconds}`;
      case 'long':
      default:
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  }

  /**
   * 获取相对时间描述（中文）
   */
  static getRelativeTime(date: Date | string): string {
    const targetTime = typeof date === 'string' ? this.utcToBeijing(new Date(date)) : this.utcToBeijing(date);
    const now = this.now();
    const diffMs = now.getTime() - targetTime.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 1) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return this.formatBeijingTime(targetTime, 'short');
    }
  }
}

// 中文文本配置
export const MESSAGES = {
  // 通用消息
  COMMON: {
    SUCCESS: '✅ 操作成功',
    FAILED: '❌ 操作失败',
    PERMISSION_DENIED: '❌ 权限不足',
    INVALID_PARAMS: '❌ 参数无效',
    UNKNOWN_ERROR: '❌ 未知错误',
    PROCESSING: '⏳ 处理中...',
    CANCELLED: '✅ 已取消',
    CONFIRMED: '✅ 已确认'
  },

  // 权限相关
  PERMISSIONS: {
    NO_PERMISSION: '❌ 您没有权限执行此操作',
    REQUIRED_ROLE: '所需角色',
    YOUR_ROLE: '您的角色',
    CONTACT_ADMIN: '请联系管理员获取权限'
  },

  // 用户角色
  ROLES: {
    USER: '普通用户',
    ADMIN: '管理员',
    SUPER_ADMIN: '超级管理员'
  },

  // DNS相关
  DNS: {
    RECORD_CREATED: '✅ DNS记录创建成功',
    RECORD_DELETED: '✅ DNS记录删除成功',
    RECORD_NOT_FOUND: '❌ DNS记录不存在',
    ALL_RECORDS_DELETED: '✅ 所有DNS记录已删除',
    NO_RECORDS: '📝 暂无DNS记录',
    PROXY_ENABLED: '🛡️ 代理已启用',
    PROXY_DISABLED: '📡 仅DNS模式',
    EXPIRY_RENEWED: '✅ 过期时间已续期',
    EXPIRY_CHECK: '📅 过期状态检查'
  },

  // 用户管理
  USER_MANAGEMENT: {
    ROLE_UPDATED: '✅ 用户角色更新成功',
    USER_NOT_FOUND: '❌ 用户不存在',
    INVALID_ROLE: '❌ 无效的角色',
    USER_LIST: '👥 用户列表',
    USER_DETAILS: '👤 用户详情'
  },

  // 时间相关
  TIME: {
    CREATED: '创建时间',
    LAST_ACTIVE: '最后活跃',
    EXPIRES_AT: '过期时间',
    NEVER: '从未',
    UNKNOWN: '未知'
  }
};
