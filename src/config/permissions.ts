// 权限配置模块

// 用户角色枚举
export enum UserRole {
  USER = 'user',
  ADMIN = 'admin', 
  SUPER_ADMIN = 'super_admin'
}

// 命令配置接口
export interface CommandConfig {
  command: string;
  requiredRole: UserRole;
  description: string;
  usage?: string;
  category: 'basic' | 'dns' | 'management' | 'admin';
  options?: string[];
}

// 回调权限配置接口
export interface CallbackPermission {
  pattern: string | RegExp;
  requiredRole: UserRole;
  description: string;
}

// 命令权限配置映射
export const COMMAND_PERMISSIONS: CommandConfig[] = [
  // 基础命令 (所有用户)
  {
    command: '/start',
    requiredRole: UserRole.USER,
    description: '开始使用机器人',
    category: 'basic'
  },
  {
    command: '/help',
    requiredRole: UserRole.USER,
    description: '显示可用命令',
    category: 'basic'
  },

  // DNS管理命令 (管理员+)
  {
    command: '/add',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '添加DNS记录',
    usage: '/add [域名] [类型] [内容] [选项]',
    category: 'dns',
    options: ['--proxy (强制启用代理)', '--no-proxy (禁用代理)']
  },
  {
    command: '/list',
    requiredRole: UserRole.ADMIN,
    description: '显示DNS记录列表',
    category: 'dns'
  },
  {
    command: '/delete',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '删除指定DNS记录',
    usage: '/delete [记录ID]',
    category: 'dns'
  },
  {
    command: '/deleteall',
    requiredRole: UserRole.ADMIN,
    description: '删除所有DNS记录 ⚠️',
    category: 'dns'
  },

  // 过期管理命令 (管理员+)
  {
    command: '/renew',
    requiredRole: UserRole.ADMIN,
    description: '续期DNS过期时间 (+24小时)',
    category: 'management'
  },
  {
    command: '/expiry',
    requiredRole: UserRole.ADMIN,
    description: '检查DNS过期状态',
    category: 'management'
  },
  {
    command: '/reminder',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '管理过期提醒设置',
    category: 'management'
  },
  {
    command: '/status',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '显示机器人和服务状态',
    category: 'management'
  },

  // 超级管理员命令
  {
    command: '/users',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '列出所有用户及其角色',
    category: 'admin'
  },
  {
    command: '/setrole',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '设置用户角色',
    usage: '/setrole [用户ID] [角色]',
    category: 'admin',
    options: ['角色: user, admin, super_admin']
  },
  {
    command: '/clearwebhook',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '清除webhook设置',
    category: 'admin'
  },
  {
    command: '/backup',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '创建DNS记录备份',
    usage: '/backup [备份原因]',
    category: 'admin'
  },
  {
    command: '/restore',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '从备份恢复DNS记录',
    usage: '/restore [备份ID]',
    category: 'admin'
  },
  {
    command: '/backups',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '查看备份列表',
    category: 'admin'
  }
];

// 回调权限配置映射
export const CALLBACK_PERMISSIONS: CallbackPermission[] = [
  // DNS管理回调 (管理员+)
  {
    pattern: /^delete_/,
    requiredRole: UserRole.ADMIN,
    description: '删除DNS记录'
  },
  {
    pattern: 'confirm_delete_all',
    requiredRole: UserRole.ADMIN,
    description: '确认删除所有DNS记录'
  },
  {
    pattern: 'cancel_delete_all',
    requiredRole: UserRole.ADMIN,
    description: '取消删除操作'
  },
  
  // 用户管理回调 (超级管理员)
  {
    pattern: /^setrole_/,
    requiredRole: UserRole.SUPER_ADMIN,
    description: '设置用户角色'
  },
  {
    pattern: 'refresh_users',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '刷新用户列表'
  },
  {
    pattern: /^userinfo_/,
    requiredRole: UserRole.SUPER_ADMIN,
    description: '查看用户详情'
  },

  // 备份管理回调 (超级管理员)
  {
    pattern: /^restore_confirm_/,
    requiredRole: UserRole.SUPER_ADMIN,
    description: '确认恢复备份'
  },
  {
    pattern: 'restore_cancel',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '取消恢复操作'
  },
  {
    pattern: /^backup_details_/,
    requiredRole: UserRole.SUPER_ADMIN,
    description: '查看备份详情'
  },
  {
    pattern: /^restore_/,
    requiredRole: UserRole.SUPER_ADMIN,
    description: '恢复备份'
  },
  {
    pattern: 'refresh_backups',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '刷新备份列表'
  },
  {
    pattern: /^delete_backup_/,
    requiredRole: UserRole.SUPER_ADMIN,
    description: '删除备份'
  },
  {
    pattern: /^delete_backup_confirm_/,
    requiredRole: UserRole.SUPER_ADMIN,
    description: '确认删除备份'
  },
  {
    pattern: 'delete_backup_cancel',
    requiredRole: UserRole.SUPER_ADMIN,
    description: '取消删除备份'
  }
];

// 权限检查工具函数
export class PermissionUtils {
  /**
   * 检查用户是否有指定权限
   */
  static hasPermission(userRole: UserRole, requiredRole: UserRole): boolean {
    const roleHierarchy = {
      [UserRole.USER]: 0,
      [UserRole.ADMIN]: 1,
      [UserRole.SUPER_ADMIN]: 2
    };
    
    return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
  }

  /**
   * 获取命令配置
   */
  static getCommandConfig(command: string): CommandConfig | undefined {
    return COMMAND_PERMISSIONS.find(cmd => cmd.command === command);
  }

  /**
   * 检查用户是否有命令权限
   */
  static hasCommandPermission(userRole: UserRole, command: string): boolean {
    const config = this.getCommandConfig(command);
    if (!config) return false;
    return this.hasPermission(userRole, config.requiredRole);
  }

  /**
   * 获取用户可用的命令列表
   */
  static getAvailableCommands(userRole: UserRole): CommandConfig[] {
    return COMMAND_PERMISSIONS.filter(cmd => this.hasPermission(userRole, cmd.requiredRole));
  }

  /**
   * 检查回调权限
   */
  static hasCallbackPermission(userRole: UserRole, callbackData: string): { 
    allowed: boolean; 
    requiredRole?: UserRole; 
    description?: string 
  } {
    for (const permission of CALLBACK_PERMISSIONS) {
      const matches = typeof permission.pattern === 'string' 
        ? callbackData === permission.pattern
        : permission.pattern.test(callbackData);
      
      if (matches) {
        return {
          allowed: this.hasPermission(userRole, permission.requiredRole),
          requiredRole: permission.requiredRole,
          description: permission.description
        };
      }
    }
    
    // 未知回调 - 默认拒绝
    return { allowed: false };
  }

  /**
   * 获取角色的中文名称
   */
  static getRoleDisplayName(role: UserRole): string {
    const roleNames = {
      [UserRole.USER]: '普通用户',
      [UserRole.ADMIN]: '管理员',
      [UserRole.SUPER_ADMIN]: '超级管理员'
    };
    return roleNames[role] || '未知角色';
  }
}
