// 统一配置入口

// 导出所有配置模块
export * from './app';
export * from './locale';
export * from './permissions';

// 重新导出常用的配置和工具
export { 
  LOCALE_CONFIG, 
  TimeUtils, 
  MESSAGES 
} from './locale';

export { 
  UserRole, 
  COMMAND_PERMISSIONS, 
  CALLBACK_PERMISSIONS, 
  PermissionUtils 
} from './permissions';

export { 
  DNS_TYPES, 
  APP_LIMITS, 
  DEFAULT_SETTINGS, 
  API_CONFIG, 
  FEATURE_FLAGS,
  APP_METADATA 
} from './app';

// 配置验证工具
export class ConfigValidator {
  /**
   * 验证环境变量是否完整
   */
  static validateEnvironment(env: any): { valid: boolean; missing: string[] } {
    const required = [
      'TELEGRAM_BOT_TOKEN',
      'CLOUDFLARE_API_TOKEN', 
      'CLOUDFLARE_ZONE_ID',
      'DB'
    ];
    
    const missing = required.filter(key => !env[key]);
    
    return {
      valid: missing.length === 0,
      missing
    };
  }

  /**
   * 验证DNS记录类型是否支持
   */
  static isValidDNSType(type: string): boolean {
    return DNS_TYPES.SUPPORTED.includes(type.toUpperCase());
  }

  /**
   * 验证DNS记录是否可以代理
   */
  static isProxyableDNSType(type: string): boolean {
    return DNS_TYPES.PROXYABLE.includes(type.toUpperCase());
  }

  /**
   * 验证用户角色是否有效
   */
  static isValidUserRole(role: string): boolean {
    return Object.values(UserRole).includes(role as UserRole);
  }
}

// 配置初始化工具
export class ConfigInitializer {
  /**
   * 初始化应用配置
   */
  static initialize(env: any) {
    // 验证环境变量
    const validation = ConfigValidator.validateEnvironment(env);
    if (!validation.valid) {
      throw new Error(`Missing required environment variables: ${validation.missing.join(', ')}`);
    }

    // 设置时区
    if (typeof process !== 'undefined' && process.env) {
      process.env.TZ = LOCALE_CONFIG.TIMEZONE;
    }

    console.log(`🚀 ${APP_METADATA.NAME} v${APP_METADATA.VERSION} 初始化完成`);
    console.log(`📍 时区: ${LOCALE_CONFIG.TIMEZONE} (UTC+${LOCALE_CONFIG.TIMEZONE_OFFSET})`);
    console.log(`🌐 语言: ${LOCALE_CONFIG.LANGUAGE}`);
    console.log(`⏰ 当前时间: ${TimeUtils.formatBeijingTime(TimeUtils.now())}`);
  }
}
