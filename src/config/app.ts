// 应用配置模块

// DNS记录类型配置
export const DNS_TYPES = {
  SUPPORTED: ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'SRV', 'NS'],
  PROXYABLE: ['A', 'AAAA', 'CNAME'], // 可以启用Cloudflare代理的记录类型
  
  // 记录类型描述
  DESCRIPTIONS: {
    'A': 'IPv4地址记录',
    'AAAA': 'IPv6地址记录', 
    'CNAME': '别名记录',
    'MX': '邮件交换记录',
    'TXT': '文本记录',
    'SRV': '服务记录',
    'NS': '域名服务器记录'
  }
};

// 应用限制配置
export const APP_LIMITS = {
  // 用户界面限制
  MAX_USERS_WITH_BUTTONS: 10, // 用户列表显示按钮的最大用户数
  MAX_MESSAGE_LENGTH: 4000,   // Telegram消息最大长度
  
  // DNS记录限制
  MAX_DNS_RECORDS_PER_USER: 100, // 每个用户最大DNS记录数
  DEFAULT_TTL: 300,              // 默认TTL值
  
  // 时间限制
  EXPIRY_EXTENSION_HOURS: 24,    // 续期延长小时数
  REMINDER_CHECK_INTERVAL: 5,    // 提醒检查间隔(分钟)
  CLEANUP_INTERVAL_HOURS: 24,    // 清理间隔(小时)
  
  // 重试限制
  MAX_RETRIES: 3,               // 最大重试次数
  RETRY_DELAY_MS: 1000          // 重试延迟(毫秒)
};

// 默认设置
export const DEFAULT_SETTINGS = {
  // DNS设置
  DNS: {
    ENABLE_PROXY_BY_DEFAULT: true,  // 默认启用代理
    DEFAULT_TTL: APP_LIMITS.DEFAULT_TTL
  },
  
  // 提醒设置
  REMINDERS: {
    ENABLED: true,                  // 默认启用提醒
    ADVANCE_HOURS: 1               // 提前1小时提醒
  },
  
  // 用户设置
  USER: {
    DEFAULT_ROLE: 'user',          // 新用户默认角色
    AUTO_CLEANUP_INACTIVE_DAYS: 30 // 自动清理非活跃用户天数
  }
};

// API配置
export const API_CONFIG = {
  // Telegram API
  TELEGRAM: {
    BASE_URL: 'https://api.telegram.org/bot',
    TIMEOUT_MS: 10000,
    MAX_RETRIES: 3
  },
  
  // Cloudflare API
  CLOUDFLARE: {
    BASE_URL: 'https://api.cloudflare.com/client/v4',
    TIMEOUT_MS: 15000,
    MAX_RETRIES: 3
  }
};

// 日志配置
export const LOG_CONFIG = {
  LEVELS: {
    ERROR: 0,
    WARN: 1,
    INFO: 2,
    DEBUG: 3
  },
  
  // 默认日志级别
  DEFAULT_LEVEL: 'INFO',
  
  // 日志格式
  FORMAT: {
    TIMESTAMP: true,
    LEVEL: true,
    MESSAGE: true,
    CONTEXT: true
  }
};

// 环境配置
export const ENV_CONFIG = {
  DEVELOPMENT: {
    LOG_LEVEL: 'DEBUG',
    ENABLE_DEBUG_LOGS: true,
    MOCK_EXTERNAL_APIS: false
  },
  
  PRODUCTION: {
    LOG_LEVEL: 'INFO',
    ENABLE_DEBUG_LOGS: false,
    MOCK_EXTERNAL_APIS: false
  }
};

// 功能开关配置
export const FEATURE_FLAGS = {
  // 核心功能
  DNS_MANAGEMENT: true,
  USER_MANAGEMENT: true,
  EXPIRY_REMINDERS: true,
  
  // 高级功能
  AUDIT_LOGGING: true,
  PERFORMANCE_MONITORING: true,
  RATE_LIMITING: false,
  
  // 实验性功能
  BULK_OPERATIONS: false,
  ADVANCED_PERMISSIONS: false,
  WEBHOOK_MANAGEMENT: true
};

// 应用元数据
export const APP_METADATA = {
  NAME: 'DNS Bot',
  VERSION: '1.0.0',
  DESCRIPTION: 'Cloudflare DNS管理机器人',
  AUTHOR: 'DNS Bot Team',
  
  // 支持信息
  SUPPORT: {
    DOCUMENTATION_URL: 'https://github.com/your-repo/dns-bot',
    ISSUE_TRACKER_URL: 'https://github.com/your-repo/dns-bot/issues',
    CONTACT_EMAIL: '<EMAIL>'
  },
  
  // 更新信息
  LAST_UPDATED: '2024-12-01',
  CHANGELOG_URL: 'https://github.com/your-repo/dns-bot/releases'
};
