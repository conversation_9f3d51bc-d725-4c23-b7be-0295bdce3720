import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { swaggerUI } from '@hono/swagger-ui';
import { app as openApiApp } from './openapi';
import { Env } from './types';
import { handleTelegramWebhook, registerTelegramWebhook } from './handlers/telegram';
import { createDNSRecord, listDNSRecords, deleteDNSRecord, deleteAllDNSRecords } from './handlers/dns';
import { cleanupExpiredRecords, validateDNSConfiguration, syncDNSRecords, generateSystemReport, checkExpiryReminders } from './handlers/cron';
import { errorHandler } from './middleware/error';
import { authMiddleware } from './middleware/auth';
import { HealthChecker, getSystemMetrics } from './utils/monitoring';
import { createLogger } from './utils/logger';

// Create main app
const app = new Hono<{ Bindings: Env }>();

// Middleware
app.use('*', cors());
app.use('*', logger());
app.use('*', prettyJSON());
app.use('*', errorHandler);

// Health check endpoints
app.get('/health', async (c) => {
  const healthChecker = new HealthChecker(c);
  const health = await healthChecker.performHealthCheck();

  const statusCode = health.overall === 'healthy' ? 200 : 503;

  return c.json({
    success: health.overall === 'healthy',
    ...health,
  }, statusCode);
});

app.get('/health/simple', (c) => {
  return c.json({
    success: true,
    message: 'DNS Bot API is running',
    timestamp: new Date().toISOString(),
    environment: c.env.ENVIRONMENT || 'unknown',
  });
});

app.get('/metrics', authMiddleware, async (c) => {
  const metrics = await getSystemMetrics(c);
  return c.json({
    success: true,
    data: metrics,
  });
});

// Swagger UI
app.get('/docs', swaggerUI({ url: '/openapi.json' }));

// OpenAPI spec
app.get('/openapi.json', (c) => {
  return c.json(openApiApp.getOpenAPIDocument({
    openapi: '3.0.0',
    info: {
      title: 'DNS Bot API',
      version: '1.0.0',
    },
  }));
});

// Telegram Bot endpoints
app.post('/telegram/register', authMiddleware, async (c) => {
  return registerTelegramWebhook(c);
});

app.post('/telegram/webhook', async (c) => {
  return handleTelegramWebhook(c);
});

// DNS management endpoints
app.post('/dns/records', authMiddleware, async (c) => {
  return createDNSRecord(c);
});

app.get('/dns/records', authMiddleware, async (c) => {
  return listDNSRecords(c);
});

app.delete('/dns/records/:id', authMiddleware, async (c) => {
  return deleteDNSRecord(c);
});

app.delete('/dns/records', authMiddleware, async (c) => {
  return deleteAllDNSRecords(c);
});

// Cron trigger handlers
app.all('/cron/cleanup', async (c) => {
  // Verify this is a cron trigger
  const cronHeader = c.req.header('CF-Cron');
  if (!cronHeader) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  return cleanupExpiredRecords(c);
});

app.all('/cron/validate', async (c) => {
  const cronHeader = c.req.header('CF-Cron');
  if (!cronHeader) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  return validateDNSConfiguration(c);
});

app.all('/cron/sync', async (c) => {
  const cronHeader = c.req.header('CF-Cron');
  if (!cronHeader) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  return syncDNSRecords(c);
});

app.all('/cron/reminders', async (c) => {
  const cronHeader = c.req.header('CF-Cron');
  if (!cronHeader) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  return checkExpiryReminders(c);
});

// DNS expiry management endpoints
app.post('/dns/renew', authMiddleware, async (c) => {
  try {
    const { renewDNSExpiry } = await import('./services/dns');
    const newExpiryTime = await renewDNSExpiry(c);

    return c.json({
      success: true,
      message: 'DNS expiry renewed successfully',
      data: {
        new_expiry_time: newExpiryTime,
        expires_in_hours: 24,
      },
    });
  } catch (error) {
    return c.json({
      success: false,
      error: 'Failed to renew DNS expiry',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, 500);
  }
});

app.get('/dns/expiry', authMiddleware, async (c) => {
  try {
    const { checkDNSExpiry } = await import('./services/dns');
    const expiryStatus = await checkDNSExpiry(c);

    return c.json({
      success: true,
      data: {
        is_expired: expiryStatus.isExpired,
        expiry_time: expiryStatus.expiryTime,
        time_remaining: expiryStatus.timeRemaining,
      },
    });
  } catch (error) {
    return c.json({
      success: false,
      error: 'Failed to check DNS expiry',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, 500);
  }
});

// Reminder management endpoints
app.get('/reminders/status', authMiddleware, async (c) => {
  try {
    const { getReminderStatus } = await import('./services/reminder');
    const status = await getReminderStatus(c);

    return c.json({
      success: true,
      data: status,
    });
  } catch (error) {
    return c.json({
      success: false,
      error: 'Failed to get reminder status',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, 500);
  }
});

app.post('/reminders/toggle', authMiddleware, async (c) => {
  try {
    const body = await c.req.json();
    const enabled = Boolean(body.enabled);

    const { toggleReminders } = await import('./services/reminder');
    await toggleReminders(c, enabled);

    return c.json({
      success: true,
      message: `Reminders ${enabled ? 'enabled' : 'disabled'} successfully`,
      data: { enabled },
    });
  } catch (error) {
    return c.json({
      success: false,
      error: 'Failed to toggle reminders',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, 500);
  }
});

app.post('/reminders/test', authMiddleware, async (c) => {
  try {
    const body = await c.req.json();
    const chatId = Number(body.chat_id);

    if (!chatId) {
      return c.json({
        success: false,
        error: 'chat_id is required',
      }, 400);
    }

    const { sendTestReminder } = await import('./services/reminder');
    const success = await sendTestReminder(c, chatId);

    return c.json({
      success,
      message: success ? 'Test reminder sent successfully' : 'Failed to send test reminder',
    });
  } catch (error) {
    return c.json({
      success: false,
      error: 'Failed to send test reminder',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, 500);
  }
});

// Admin endpoints
app.get('/admin/system-report', authMiddleware, async (c) => {
  return generateSystemReport(c);
});

// 404 handler
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'Not Found',
    message: 'The requested endpoint was not found',
    code: 404,
  }, 404);
});

// Export for Cloudflare Workers
export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    return app.fetch(request, env, ctx);
  },

  async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
    // Handle different cron triggers based on schedule
    const cron = event.cron;

    // Create a minimal context object for cron tasks
    const cronContext = { env } as any;

    if (cron === '0 0 * * *') {
      // Daily cleanup at midnight
      ctx.waitUntil(cleanupExpiredRecords(cronContext));
    } else if (cron === '*/5 * * * *') {
      // Reminder check every 5 minutes
      ctx.waitUntil(checkExpiryReminders(cronContext));
    }
  },
};
