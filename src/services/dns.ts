import { Context } from 'hono';
import { Env, DNSR<PERSON>ord, DNSRecordWithMeta } from '../types';
import { createCloudflareAPI, validateDNSRecord } from './cloudflare';
import { getBotSetting, setBotSetting } from './database';
import { createDNSBackup } from './backup';
import { notifyDNSRecordsDeleted } from './notification';

export async function createDNSRecordFromBot(
  c: Context<{ Bindings: Env }>,
  recordData: {
    name: string;
    type: string;
    content: string;
    ttl?: number;
    proxied?: boolean;
    priority?: number;
  },
  userId: number,
  chatId: number
): Promise<DNSRecord> {
  // Validate DNS record
  await validateDNSRecord(recordData);

  // Check user limits by counting existing records
  const userRecords = await listUserDNSRecords(c, userId);
  const maxRecords = 10; // Could be configurable

  if (userRecords.length >= maxRecords) {
    throw new Error(`Maximum number of DNS records (${maxRecords}) reached for this user`);
  }

  // Create Cloudflare API client
  const cfApi = createCloudflareAPI(c);

  // Create DNS record in Cloudflare
  const cfRecord = await cfApi.createDNSRecord(recordData);

  return cfRecord;
}

export async function deleteDNSRecordFromBot(
  c: Context<{ Bindings: Env }>,
  recordId: string,
  userId: number
): Promise<void> {
  // Create Cloudflare API client
  const cfApi = createCloudflareAPI(c);

  // Get record from Cloudflare to verify it exists
  try {
    await cfApi.getDNSRecord(recordId);
  } catch (error) {
    throw new Error('DNS record not found');
  }

  // Delete from Cloudflare
  await cfApi.deleteDNSRecord(recordId);
}

export async function listUserDNSRecords(
  c: Context<{ Bindings: Env }>,
  userId: number
): Promise<DNSRecord[]> {
  // Create Cloudflare API client
  const cfApi = createCloudflareAPI(c);

  // Get all DNS records from Cloudflare
  const result = await cfApi.listDNSRecords();

  // Since we don't store user associations in database anymore,
  // we return all records. In a real implementation, you might want to
  // filter by record name patterns or use comments/tags to identify user records
  return result.records;
}

export async function deleteAllUserDNSRecords(
  c: Context<{ Bindings: Env }>,
  userId: number,
  reason: string = '手动删除所有DNS记录'
): Promise<{ deleted: number; errors: string[]; backupId?: string }> {
  // Create Cloudflare API client
  const cfApi = createCloudflareAPI(c);

  // Get all DNS records from Cloudflare
  const result = await cfApi.listDNSRecords();
  const allRecords = result.records;

  if (allRecords.length === 0) {
    return { deleted: 0, errors: [] };
  }

  // 在删除前创建备份
  console.log(`Creating backup before deleting ${allRecords.length} DNS records`);
  const backupResult = await createDNSBackup(c, userId, reason, 'manual');

  if (!backupResult.success) {
    console.error('Failed to create backup before deletion:', backupResult.error);
    // 如果备份失败，仍然继续删除，但记录错误
  }

  let deletedCount = 0;
  const errors: string[] = [];

  // Delete each record
  for (const record of allRecords) {
    try {
      // Delete from Cloudflare
      await cfApi.deleteDNSRecord(record.id!);
      deletedCount++;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(`Record ${record.name} (${record.id}): ${errorMessage}`);
      console.error(`Failed to delete record ${record.id}:`, error);
    }
  }

  // 发送删除通知给管理员
  if (deletedCount > 0) {
    try {
      await notifyDNSRecordsDeleted(c, deletedCount, reason, backupResult.backupId, userId);
      console.log(`Sent deletion notification to admins: ${deletedCount} records deleted`);
    } catch (error) {
      console.error('Failed to send deletion notification:', error);
    }
  }

  return {
    deleted: deletedCount,
    errors,
    backupId: backupResult.backupId
  };
}

export async function updateDNSRecordFromBot(
  c: Context<{ Bindings: Env }>,
  recordId: string,
  updates: {
    content?: string;
    ttl?: number;
    proxied?: boolean;
  },
  userId: number
): Promise<DNSRecord> {
  // Create Cloudflare API client
  const cfApi = createCloudflareAPI(c);

  // Update in Cloudflare
  const cfRecord = await cfApi.updateDNSRecord(recordId, updates);

  return cfRecord;
}

export async function getDNSRecordInfo(
  c: Context<{ Bindings: Env }>,
  recordId: string,
  userId: number
): Promise<DNSRecord> {
  // Create Cloudflare API client
  const cfApi = createCloudflareAPI(c);

  // Get record from Cloudflare
  const record = await cfApi.getDNSRecord(recordId);

  return record;
}

export async function searchDNSRecords(
  c: Context<{ Bindings: Env }>,
  userId: number,
  query: string
): Promise<DNSRecord[]> {
  // Create Cloudflare API client
  const cfApi = createCloudflareAPI(c);

  // Search DNS records by name
  const result = await cfApi.listDNSRecords({ name: query });

  return result.records;
}

export async function getUserDNSStats(
  c: Context<{ Bindings: Env }>,
  userId: number
): Promise<{
  total: number;
  by_type: Record<string, number>;
  recent_count: number;
}> {
  // Create Cloudflare API client
  const cfApi = createCloudflareAPI(c);

  // Get all DNS records from Cloudflare
  const result = await cfApi.listDNSRecords();
  const records = result.records;

  // Calculate statistics
  const total = records.length;
  const by_type: Record<string, number> = {};

  records.forEach(record => {
    by_type[record.type] = (by_type[record.type] || 0) + 1;
  });

  return {
    total,
    by_type,
    recent_count: total, // Since we don't have creation dates, return total
  };
}

export async function checkDNSExpiry(c: Context<{ Bindings: Env }>): Promise<{
  isExpired: boolean;
  expiryTime: string | null;
  timeRemaining?: string;
}> {
  const expiryTime = await getBotSetting(c, 'dns_expiry_time');

  if (!expiryTime) {
    return {
      isExpired: false,
      expiryTime: null,
    };
  }

  const now = new Date();
  const expiry = new Date(expiryTime);
  const isExpired = now > expiry;

  let timeRemaining: string | undefined;
  if (!isExpired) {
    const diffMs = expiry.getTime() - now.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffHours > 0) {
      timeRemaining = `${diffHours}小时${diffMinutes}分钟`;
    } else {
      timeRemaining = `${diffMinutes}分钟`;
    }
  }

  return {
    isExpired,
    expiryTime,
    timeRemaining,
  };
}

export async function renewDNSExpiry(c: Context<{ Bindings: Env }>): Promise<string> {
  const newExpiryTime = new Date();
  newExpiryTime.setHours(newExpiryTime.getHours() + 24);

  const expiryTimeString = newExpiryTime.toISOString();
  await setBotSetting(c, 'dns_expiry_time', expiryTimeString);

  return expiryTimeString;
}

export async function deleteAllDNSRecordsIfExpired(c: Context<{ Bindings: Env }>): Promise<{
  wasExpired: boolean;
  deleted: number;
  errors: string[];
}> {
  const expiryCheck = await checkDNSExpiry(c);

  if (!expiryCheck.isExpired) {
    return {
      wasExpired: false,
      deleted: 0,
      errors: [],
    };
  }

  // DNS已过期，删除所有记录（系统自动清理）
  const deleteResult = await deleteAllUserDNSRecords(c, 0, 'DNS过期自动清理');

  // 发送过期清理通知给管理员
  if (deleteResult.deleted > 0) {
    try {
      await notifyDNSRecordsDeleted(
        c,
        deleteResult.deleted,
        `DNS记录已过期自动清理 (过期时间: ${expiryCheck.expiryTime})`,
        deleteResult.backupId
      );
      console.log(`Sent expiry cleanup notification: ${deleteResult.deleted} records deleted`);
    } catch (error) {
      console.error('Failed to send expiry cleanup notification:', error);
    }
  }

  // 重置过期时间（可选，或者保持过期状态）
  // await renewDNSExpiry(c);

  return {
    wasExpired: true,
    deleted: deleteResult.deleted,
    errors: deleteResult.errors,
  };
}

export async function checkExpiryReminder(c: Context<{ Bindings: Env }>): Promise<{
  needsReminder: boolean;
  timeUntilExpiry?: number; // minutes
  expiryTime?: string;
  lastReminderTime?: string;
}> {
  const expiryCheck = await checkDNSExpiry(c);

  if (!expiryCheck.expiryTime || expiryCheck.isExpired) {
    return { needsReminder: false };
  }

  const now = new Date();
  const expiry = new Date(expiryCheck.expiryTime);
  const timeUntilExpiryMs = expiry.getTime() - now.getTime();
  const timeUntilExpiryMinutes = Math.floor(timeUntilExpiryMs / (1000 * 60));

  // 检查是否在过期前1小时内
  if (timeUntilExpiryMinutes > 60) {
    return { needsReminder: false };
  }

  // 检查上次提醒时间
  const lastReminderTime = await getBotSetting(c, 'last_reminder_time');
  const reminderEnabled = await getBotSetting(c, 'reminder_enabled');

  if (reminderEnabled === 'false') {
    return { needsReminder: false };
  }

  let shouldSendReminder = true;

  if (lastReminderTime) {
    const lastReminder = new Date(lastReminderTime);
    const timeSinceLastReminder = now.getTime() - lastReminder.getTime();
    const minutesSinceLastReminder = Math.floor(timeSinceLastReminder / (1000 * 60));

    // 如果距离上次提醒不足5分钟，不发送提醒
    if (minutesSinceLastReminder < 5) {
      shouldSendReminder = false;
    }
  }

  return {
    needsReminder: shouldSendReminder,
    timeUntilExpiry: timeUntilExpiryMinutes,
    expiryTime: expiryCheck.expiryTime,
    lastReminderTime: lastReminderTime || undefined,
  };
}

export async function updateLastReminderTime(c: Context<{ Bindings: Env }>): Promise<void> {
  const now = new Date().toISOString();
  await setBotSetting(c, 'last_reminder_time', now);
}

export async function checkDNSExpiryWarning(c: Context<{ Bindings: Env }>): Promise<{
  needsWarning: boolean;
  expiryTime: string | null;
  timeRemaining?: string;
  minutesRemaining?: number;
}> {
  const expiryTime = await getBotSetting(c, 'dns_expiry_time');

  if (!expiryTime) {
    return {
      needsWarning: false,
      expiryTime: null,
    };
  }

  const now = new Date();
  const expiry = new Date(expiryTime);
  const diffMs = expiry.getTime() - now.getTime();
  const minutesRemaining = Math.floor(diffMs / (1000 * 60));

  // 检查是否需要警告（过期前1小时内，且未过期）
  const needsWarning = minutesRemaining > 0 && minutesRemaining <= 60;

  let timeRemaining: string | undefined;
  if (minutesRemaining > 0) {
    const hours = Math.floor(minutesRemaining / 60);
    const mins = minutesRemaining % 60;

    if (hours > 0) {
      timeRemaining = `${hours}小时${mins}分钟`;
    } else {
      timeRemaining = `${mins}分钟`;
    }
  }

  return {
    needsWarning,
    expiryTime,
    timeRemaining,
    minutesRemaining: minutesRemaining > 0 ? minutesRemaining : 0,
  };
}

export async function getLastWarningTime(c: Context<{ Bindings: Env }>): Promise<string | null> {
  return await getBotSetting(c, 'last_warning_time');
}

export async function setLastWarningTime(c: Context<{ Bindings: Env }>, time: string): Promise<void> {
  await setBotSetting(c, 'last_warning_time', time);
}
