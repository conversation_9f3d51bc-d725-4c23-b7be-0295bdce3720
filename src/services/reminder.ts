import { Context } from 'hono';
import { Env } from '../types';
import { checkExpiryReminder, updateLastReminderTime } from './dns';
import { logAuditEvent } from './database';

export async function sendExpiryReminder(
  c: Context<{ Bindings: Env }>,
  chatId: number,
  timeUntilExpiry: number,
  expiryTime: string
): Promise<boolean> {
  try {
    const botToken = c.env.TELEGRAM_BOT_TOKEN;
    
    // 格式化剩余时间
    let timeMessage: string;
    if (timeUntilExpiry <= 0) {
      timeMessage = '已过期';
    } else if (timeUntilExpiry < 60) {
      timeMessage = `${timeUntilExpiry}分钟`;
    } else {
      const hours = Math.floor(timeUntilExpiry / 60);
      const minutes = timeUntilExpiry % 60;
      timeMessage = `${hours}小时${minutes}分钟`;
    }

    const message = `⚠️ DNS过期提醒 ⚠️\n\n` +
      `🕐 过期时间: ${new Date(expiryTime).toLocaleString('zh-CN')}\n` +
      `⏰ 剩余时间: ${timeMessage}\n\n` +
      `🚨 您的DNS记录即将过期！\n` +
      `过期后所有DNS记录将被自动删除。\n\n` +
      `🔄 请使用 /renew 命令立即续期 24 小时\n` +
      `📊 使用 /expiry 命令查看详细状态`;

    const response = await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: chatId,
        text: message,
        // parse_mode: 'HTML', // Disabled to avoid HTML parsing issues
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to send reminder message:', errorText);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error sending expiry reminder:', error);
    return false;
  }
}

export async function checkAndSendReminders(c: Context<{ Bindings: Env }>): Promise<{
  remindersSent: number;
  errors: string[];
}> {
  try {
    // 检查是否需要发送提醒
    const reminderCheck = await checkExpiryReminder(c);
    
    if (!reminderCheck.needsReminder) {
      return {
        remindersSent: 0,
        errors: [],
      };
    }

    // 获取所有活跃用户的chat_id
    const activeUsers = await getActiveUsers(c);
    
    if (activeUsers.length === 0) {
      return {
        remindersSent: 0,
        errors: ['No active users found'],
      };
    }

    let successCount = 0;
    const errors: string[] = [];

    // 向所有活跃用户发送提醒
    for (const user of activeUsers) {
      try {
        const success = await sendExpiryReminder(
          c,
          user.chat_id,
          reminderCheck.timeUntilExpiry!,
          reminderCheck.expiryTime!
        );

        if (success) {
          successCount++;
        } else {
          errors.push(`Failed to send reminder to user ${user.id} (chat: ${user.chat_id})`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Error sending to user ${user.id}: ${errorMessage}`);
      }
    }

    // 更新最后提醒时间
    if (successCount > 0) {
      await updateLastReminderTime(c);
      
      // 记录审计日志
      await logAuditEvent(
        c,
        0, // System user
        'SEND_EXPIRY_REMINDER',
        'reminder',
        'expiry_warning',
        `Sent expiry reminders to ${successCount} users. Time until expiry: ${reminderCheck.timeUntilExpiry} minutes`
      );
    }

    return {
      remindersSent: successCount,
      errors,
    };

  } catch (error) {
    console.error('Error in checkAndSendReminders:', error);
    return {
      remindersSent: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error'],
    };
  }
}

async function getActiveUsers(c: Context<{ Bindings: Env }>): Promise<Array<{ id: number; chat_id: number }>> {
  try {
    // 获取最近7天内活跃的用户
    const stmt = c.env.DB.prepare(`
      SELECT id, chat_id 
      FROM users 
      WHERE chat_id IS NOT NULL 
      AND chat_id != 0
      AND last_active >= datetime('now', '-7 days')
      ORDER BY last_active DESC
    `);
    
    const results = await stmt.all();
    
    return results.results.map((row: any) => ({
      id: row.id,
      chat_id: row.chat_id,
    }));
  } catch (error) {
    console.error('Error getting active users:', error);
    return [];
  }
}

export async function sendTestReminder(
  c: Context<{ Bindings: Env }>,
  chatId: number
): Promise<boolean> {
  try {
    const reminderCheck = await checkExpiryReminder(c);
    
    if (!reminderCheck.expiryTime) {
      // 发送测试消息，说明没有设置过期时间
      const message = `🧪 测试提醒\n\n` +
        `⚠️ 当前未设置DNS过期时间\n` +
        `请使用 /renew 命令设置过期时间`;

      const botToken = c.env.TELEGRAM_BOT_TOKEN;
      const response = await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: chatId,
          text: message,
        }),
      });

      return response.ok;
    }

    // 发送实际的提醒消息
    return await sendExpiryReminder(
      c,
      chatId,
      reminderCheck.timeUntilExpiry || 0,
      reminderCheck.expiryTime
    );
  } catch (error) {
    console.error('Error sending test reminder:', error);
    return false;
  }
}

export async function toggleReminders(c: Context<{ Bindings: Env }>, enabled: boolean): Promise<void> {
  const { setBotSetting } = await import('./database');
  await setBotSetting(c, 'reminder_enabled', enabled ? 'true' : 'false');
}

export async function getReminderStatus(c: Context<{ Bindings: Env }>): Promise<{
  enabled: boolean;
  lastReminderTime: string | null;
  nextCheckIn?: string;
}> {
  const { getBotSetting } = await import('./database');
  
  const enabled = await getBotSetting(c, 'reminder_enabled') !== 'false';
  const lastReminderTime = await getBotSetting(c, 'last_reminder_time');
  
  let nextCheckIn: string | undefined;
  if (enabled && lastReminderTime) {
    const lastReminder = new Date(lastReminderTime);
    const nextCheck = new Date(lastReminder.getTime() + 5 * 60 * 1000); // 5分钟后
    const now = new Date();
    
    if (nextCheck > now) {
      const diffMs = nextCheck.getTime() - now.getTime();
      const diffMinutes = Math.ceil(diffMs / (1000 * 60));
      nextCheckIn = `${diffMinutes}分钟`;
    } else {
      nextCheckIn = '现在';
    }
  }

  return {
    enabled,
    lastReminderTime,
    nextCheckIn,
  };
}
