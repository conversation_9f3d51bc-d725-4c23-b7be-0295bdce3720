import { Context } from 'hono';
import { Env } from '../types';
import { logAuditEvent } from './database';
import { createCloudflareAPI } from './cloudflare';
import { log } from '../utils/helpers';

export interface DNSRecordBackup {
  id: string;
  name: string;
  type: string;
  content: string;
  proxied: boolean;
  ttl: number;
  zone_id: string;
  zone_name: string;
  created_on: string;
  modified_on: string;
  meta?: any;
  data?: any;
}

export interface BackupMetadata {
  id: string;
  created_at: string;
  created_by: number;
  reason: string;
  record_count: number;
  zone_id: string;
  zone_name: string;
  backup_type: 'manual' | 'auto_cleanup' | 'auto_expiry';
}

/**
 * 创建DNS记录备份
 */
export async function createDNSBackup(
  c: Context<{ Bindings: Env }>, 
  userId: number, 
  reason: string,
  backupType: 'manual' | 'auto_cleanup' | 'auto_expiry' = 'manual'
): Promise<{ success: boolean; backupId?: string; recordCount?: number; error?: string }> {
  try {
    log('info', `Creating DNS backup - User: ${userId}, Reason: ${reason}`);

    // 获取当前所有DNS记录
    const cf = createCloudflareAPI(c);
    const result = await cf.listDNSRecords();

    const records = result.records as DNSRecordBackup[];
    
    if (records.length === 0) {
      log('info', 'No DNS records to backup');
      return { success: true, recordCount: 0 };
    }

    // 生成备份ID
    const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = new Date().toISOString();

    // 获取zone信息 - 暂时使用环境变量中的zone ID作为名称
    const zoneName = c.env.CLOUDFLARE_ZONE_ID;

    // 保存备份元数据
    const metadata: BackupMetadata = {
      id: backupId,
      created_at: timestamp,
      created_by: userId,
      reason,
      record_count: records.length,
      zone_id: c.env.CLOUDFLARE_ZONE_ID,
      zone_name: zoneName,
      backup_type: backupType
    };

    await c.env.DB.prepare(`
      INSERT INTO dns_backups (
        backup_id, created_at, created_by, reason, record_count, 
        zone_id, zone_name, backup_type, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      backupId,
      timestamp,
      userId,
      reason,
      records.length,
      c.env.CLOUDFLARE_ZONE_ID,
      zoneName,
      backupType,
      JSON.stringify(metadata)
    ).run();

    // 保存每个DNS记录
    for (const record of records) {
      await c.env.DB.prepare(`
        INSERT INTO dns_backup_records (
          backup_id, record_id, name, type, content, proxied, ttl,
          zone_id, zone_name, created_on, modified_on, record_data
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        backupId,
        record.id,
        record.name,
        record.type,
        record.content,
        record.proxied ? 1 : 0,
        record.ttl,
        record.zone_id,
        record.zone_name || zoneName,
        record.created_on,
        record.modified_on,
        JSON.stringify(record)
      ).run();
    }

    // 记录审计日志
    await logAuditEvent(
      c,
      userId,
      'CREATE_DNS_BACKUP',
      'dns_backup',
      backupId,
      `Created backup with ${records.length} records - Reason: ${reason}`
    );

    log('info', `DNS backup created successfully - ID: ${backupId}, Records: ${records.length}`);

    return {
      success: true,
      backupId,
      recordCount: records.length
    };

  } catch (error) {
    log('error', 'Failed to create DNS backup:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 获取备份列表
 */
export async function listDNSBackups(
  c: Context<{ Bindings: Env }>,
  limit: number = 10
): Promise<BackupMetadata[]> {
  try {
    const result = await c.env.DB.prepare(`
      SELECT backup_id, created_at, created_by, reason, record_count,
             zone_id, zone_name, backup_type, metadata
      FROM dns_backups
      ORDER BY created_at DESC
      LIMIT ?
    `).bind(limit).all();

    return result.results.map((row: any) => ({
      id: row.backup_id,
      created_at: row.created_at,
      created_by: row.created_by,
      reason: row.reason,
      record_count: row.record_count,
      zone_id: row.zone_id,
      zone_name: row.zone_name,
      backup_type: row.backup_type
    }));

  } catch (error) {
    log('error', 'Failed to list DNS backups:', error);
    return [];
  }
}

/**
 * 获取备份详情
 */
export async function getBackupDetails(
  c: Context<{ Bindings: Env }>,
  backupId: string
): Promise<{ metadata: BackupMetadata | null; records: DNSRecordBackup[] }> {
  try {
    // 获取备份元数据
    const metadataResult = await c.env.DB.prepare(`
      SELECT backup_id, created_at, created_by, reason, record_count,
             zone_id, zone_name, backup_type, metadata
      FROM dns_backups
      WHERE backup_id = ?
    `).bind(backupId).first();

    if (!metadataResult) {
      return { metadata: null, records: [] };
    }

    const metadata: BackupMetadata = {
      id: metadataResult.backup_id as string,
      created_at: metadataResult.created_at as string,
      created_by: metadataResult.created_by as number,
      reason: metadataResult.reason as string,
      record_count: metadataResult.record_count as number,
      zone_id: metadataResult.zone_id as string,
      zone_name: metadataResult.zone_name as string,
      backup_type: metadataResult.backup_type as 'manual' | 'auto_cleanup' | 'auto_expiry'
    };

    // 获取备份记录
    const recordsResult = await c.env.DB.prepare(`
      SELECT record_id, name, type, content, proxied, ttl,
             zone_id, zone_name, created_on, modified_on, record_data
      FROM dns_backup_records
      WHERE backup_id = ?
      ORDER BY name, type
    `).bind(backupId).all();

    const records: DNSRecordBackup[] = recordsResult.results.map((row: any) => {
      const recordData = JSON.parse(row.record_data as string);
      return {
        id: row.record_id,
        name: row.name,
        type: row.type,
        content: row.content,
        proxied: Boolean(row.proxied),
        ttl: row.ttl,
        zone_id: row.zone_id,
        zone_name: row.zone_name,
        created_on: row.created_on,
        modified_on: row.modified_on,
        ...recordData
      };
    });

    return { metadata, records };

  } catch (error) {
    log('error', 'Failed to get backup details:', error);
    return { metadata: null, records: [] };
  }
}

/**
 * 恢复DNS记录从备份
 */
export async function restoreDNSFromBackup(
  c: Context<{ Bindings: Env }>,
  backupId: string,
  userId: number,
  clearExisting: boolean = true
): Promise<{ success: boolean; restored: number; errors: string[]; error?: string }> {
  try {
    log('info', `Starting DNS restore from backup: ${backupId}`);

    // 获取备份详情
    const { metadata, records } = await getBackupDetails(c, backupId);
    
    if (!metadata || records.length === 0) {
      throw new Error('Backup not found or contains no records');
    }

    const cf = createCloudflareAPI(c);
    const errors: string[] = [];
    let restored = 0;

    // 如果需要，先清除现有记录
    if (clearExisting) {
      log('info', 'Clearing existing DNS records before restore');

      const existingResult = await cf.listDNSRecords({ per_page: 100 });
      const existingRecords = existingResult.records;

      for (const record of existingRecords) {
        try {
          await cf.deleteDNSRecord(record.id!);
        } catch (error) {
          log('warn', `Failed to delete existing record ${record.id}:`, error);
        }
      }
    }

    // 恢复备份的记录
    for (const record of records) {
      try {
        const createData = {
          name: record.name,
          type: record.type,
          content: record.content,
          proxied: record.proxied,
          ttl: record.ttl
        };

        // 添加特定类型的额外数据
        if (record.data) {
          Object.assign(createData, { data: record.data });
        }

        const response = await cf.post(`zones/${c.env.CLOUDFLARE_ZONE_ID}/dns_records`, createData);
        
        if (response.success) {
          restored++;
          log('info', `Restored DNS record: ${record.name} ${record.type} ${record.content}`);
        } else {
          const errorMsg = `Failed to restore ${record.name}: ${response.errors?.[0]?.message || 'Unknown error'}`;
          errors.push(errorMsg);
          log('error', errorMsg);
        }

      } catch (error) {
        const errorMsg = `Error restoring ${record.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
        log('error', errorMsg);
      }
    }

    // 记录审计日志
    await logAuditEvent(
      c,
      userId,
      'RESTORE_DNS_BACKUP',
      'dns_backup',
      backupId,
      `Restored ${restored}/${records.length} DNS records from backup. Errors: ${errors.length}`
    );

    log('info', `DNS restore completed - Restored: ${restored}/${records.length}, Errors: ${errors.length}`);

    return {
      success: true,
      restored,
      errors
    };

  } catch (error) {
    log('error', 'Failed to restore DNS from backup:', error);
    return {
      success: false,
      restored: 0,
      errors: [],
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 删除备份
 */
export async function deleteBackup(
  c: Context<{ Bindings: Env }>,
  backupId: string,
  userId: number
): Promise<{ success: boolean; error?: string }> {
  try {
    // 删除备份记录
    await c.env.DB.prepare(`DELETE FROM dns_backup_records WHERE backup_id = ?`).bind(backupId).run();
    
    // 删除备份元数据
    const result = await c.env.DB.prepare(`DELETE FROM dns_backups WHERE backup_id = ?`).bind(backupId).run();
    
    if (result.meta.changes === 0) {
      throw new Error('Backup not found');
    }

    // 记录审计日志
    await logAuditEvent(
      c,
      userId,
      'DELETE_DNS_BACKUP',
      'dns_backup',
      backupId,
      'Deleted DNS backup'
    );

    log('info', `DNS backup deleted: ${backupId}`);

    return { success: true };

  } catch (error) {
    log('error', 'Failed to delete backup:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
