import { Context } from 'hono';
import { Env } from '../types';
import { logAuditEvent, getBotSetting } from '../services/database';
import { createCloudflareAPI } from '../services/cloudflare';
import { log } from '../utils/helpers';

export async function cleanupExpiredRecords(c: Context<{ Bindings: Env }>) {
  try {
    log('info', 'Starting scheduled cleanup task - checking DNS expiry');

    // Check if cleanup is enabled
    const cleanupEnabled = await getBotSetting(c, 'cleanup_enabled');
    if (cleanupEnabled === 'false') {
      log('info', 'Cleanup is disabled, skipping');
      return c.json({
        success: true,
        message: 'Cleanup is disabled',
        processed: 0,
      });
    }

    // Import DNS functions
    const { deleteAllDNSRecordsIfExpired, checkDNSExpiry } = await import('../services/dns');

    // Check if DNS records have expired
    const expiryCheck = await checkDNSExpiry(c);

    if (expiryCheck.isExpired) {
      log('info', `DNS records have expired (expiry: ${expiryCheck.expiryTime}), deleting all records`);

      // Delete all DNS records due to expiration
      const deleteResult = await deleteAllDNSRecordsIfExpired(c);

      // Log audit event
      await logAuditEvent(
        c,
        0, // System user
        'CLEANUP_EXPIRED_DNS',
        'dns_records',
        'all',
        `Deleted ${deleteResult.deleted} DNS records due to expiration. Errors: ${deleteResult.errors.length}`
      );

      log('info', `Expiry cleanup completed: ${deleteResult.deleted} records deleted, ${deleteResult.errors.length} errors`);

      return c.json({
        success: true,
        message: `DNS records expired and deleted: ${deleteResult.deleted} records processed`,
        data: {
          expired: true,
          deleted: deleteResult.deleted,
          errors: deleteResult.errors.length,
          error_details: deleteResult.errors.length > 0 ? deleteResult.errors : undefined,
        },
      });
    } else {
      log('info', `DNS records not expired. Time remaining: ${expiryCheck.timeRemaining || 'unknown'}`);
    }

    // Perform general maintenance tasks
    let tasksCompleted = 0;

    // Task 1: Clean up old audit logs (older than 30 days)
    try {
      const cleanupStmt = c.env.DB.prepare(`
        DELETE FROM audit_log
        WHERE created_at < datetime('now', '-30 days')
      `);
      const result = await cleanupStmt.run();
      log('info', `Cleaned up ${result.changes} old audit log entries`);
      tasksCompleted++;
    } catch (error) {
      log('error', 'Failed to cleanup old audit logs:', error);
    }

    // Task 2: Update user activity (mark inactive users)
    try {
      const updateStmt = c.env.DB.prepare(`
        UPDATE users
        SET last_active = last_active
        WHERE last_active < datetime('now', '-7 days')
      `);
      await updateStmt.run();
      tasksCompleted++;
    } catch (error) {
      log('error', 'Failed to update user activity:', error);
    }

    // Log audit event
    await logAuditEvent(
      c,
      0, // System user
      'CLEANUP_MAINTENANCE',
      'system',
      'cleanup',
      `Completed ${tasksCompleted} maintenance tasks. DNS expiry: ${expiryCheck.expiryTime}, remaining: ${expiryCheck.timeRemaining || 'N/A'}`
    );

    log('info', `Cleanup completed: ${tasksCompleted} tasks processed`);

    return c.json({
      success: true,
      message: `Cleanup completed: ${tasksCompleted} maintenance tasks processed`,
      data: {
        expired: false,
        maintenance_tasks: tasksCompleted,
        dns_expiry: expiryCheck.expiryTime,
        time_remaining: expiryCheck.timeRemaining,
      },
    });

  } catch (error) {
    log('error', 'Cleanup job failed:', error);

    return c.json({
      success: false,
      error: 'Cleanup job failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, 500);
  }
}

export async function validateDNSConfiguration(c: Context<{ Bindings: Env }>) {
  try {
    log('info', 'Starting DNS configuration validation');

    // Create Cloudflare API client
    const cfApi = createCloudflareAPI(c);

    // Verify zone access
    const hasAccess = await cfApi.verifyZoneAccess();
    if (!hasAccess) {
      throw new Error('Cannot access Cloudflare zone');
    }

    // Get zone information
    const zoneInfo = await cfApi.getZoneInfo();

    // Get current DNS records count
    const recordsResult = await cfApi.listDNSRecords();
    const recordCount = recordsResult.records.length;

    log('info', `DNS validation completed: Zone ${zoneInfo.name}, ${recordCount} records`);

    // Log audit event
    await logAuditEvent(
      c,
      0, // System user
      'DNS_VALIDATION',
      'system',
      'validation',
      `DNS validation completed: Zone ${zoneInfo.name}, ${recordCount} records`
    );

    return c.json({
      success: true,
      message: `DNS validation completed successfully`,
      data: {
        zone_name: zoneInfo.name,
        zone_id: zoneInfo.id,
        record_count: recordCount,
        zone_status: zoneInfo.status,
      },
    });

  } catch (error) {
    log('error', 'DNS validation failed:', error);

    return c.json({
      success: false,
      error: 'DNS validation failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, 500);
  }
}

export async function syncDNSRecords(c: Context<{ Bindings: Env }>) {
  try {
    log('info', 'Starting DNS records status check');

    // Create Cloudflare API client
    const cfApi = createCloudflareAPI(c);

    // Get all records from Cloudflare
    const cfRecords = await cfApi.listDNSRecords();

    // Since we no longer store DNS records locally, this function
    // now serves as a status check and reporting function

    const recordsByType: Record<string, number> = {};
    const recordsByStatus: Record<string, number> = {};

    cfRecords.records.forEach(record => {
      recordsByType[record.type] = (recordsByType[record.type] || 0) + 1;
      const status = record.proxied ? 'proxied' : 'dns_only';
      recordsByStatus[status] = (recordsByStatus[status] || 0) + 1;
    });

    log('info', `DNS status check completed: ${cfRecords.records.length} total records`);

    // Log audit event
    await logAuditEvent(
      c,
      0, // System user
      'DNS_STATUS_CHECK',
      'system',
      'status_check',
      `DNS status check: ${cfRecords.records.length} records, types: ${JSON.stringify(recordsByType)}`
    );

    return c.json({
      success: true,
      message: `DNS status check completed: ${cfRecords.records.length} records found`,
      data: {
        total_records: cfRecords.records.length,
        records_by_type: recordsByType,
        records_by_status: recordsByStatus,
        timestamp: new Date().toISOString(),
      },
    });

  } catch (error) {
    log('error', 'DNS status check failed:', error);

    return c.json({
      success: false,
      error: 'DNS status check failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, 500);
  }
}

export async function generateSystemReport(c: Context<{ Bindings: Env }>) {
  try {
    // Create Cloudflare API client
    const cfApi = createCloudflareAPI(c);

    // Get DNS records from Cloudflare
    const cfRecords = await cfApi.listDNSRecords();
    const totalRecords = cfRecords.records.length;

    // Get system statistics from database
    const totalUsersStmt = c.env.DB.prepare('SELECT COUNT(*) as count FROM users');
    const totalUsersResult = await totalUsersStmt.first();
    const totalUsers = totalUsersResult?.count as number || 0;

    const recentActivityStmt = c.env.DB.prepare(`
      SELECT COUNT(*) as count FROM audit_log
      WHERE created_at >= datetime('now', '-7 days')
    `);
    const recentActivityResult = await recentActivityStmt.first();
    const recentActivity = recentActivityResult?.count as number || 0;

    // Get maintenance history from audit log
    const maintenanceHistoryStmt = c.env.DB.prepare(`
      SELECT COUNT(*) as count, DATE(created_at) as date, action
      FROM audit_log
      WHERE action LIKE 'CLEANUP_%' OR action LIKE 'DNS_%'
      AND created_at >= datetime('now', '-30 days')
      GROUP BY DATE(created_at), action
      ORDER BY date DESC
    `);
    const maintenanceHistory = await maintenanceHistoryStmt.all();

    const report = {
      timestamp: new Date().toISOString(),
      dns_statistics: {
        total_records: totalRecords,
        records_by_type: cfRecords.records.reduce((acc: Record<string, number>, record) => {
          acc[record.type] = (acc[record.type] || 0) + 1;
          return acc;
        }, {}),
      },
      system_statistics: {
        total_users: totalUsers,
        recent_activity: recentActivity,
      },
      maintenance_history: maintenanceHistory.results,
      next_maintenance: 'Daily at 00:00 UTC',
    };

    return c.json({
      success: true,
      data: report,
    });

  } catch (error) {
    log('error', 'Failed to generate system report:', error);

    return c.json({
      success: false,
      error: 'Failed to generate system report',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, 500);
  }
}

export async function checkExpiryReminders(c: Context<{ Bindings: Env }>) {
  try {
    log('info', 'Starting expiry reminder check');

    // Import reminder functions
    const { checkAndSendReminders } = await import('../services/reminder');

    // Check and send reminders
    const result = await checkAndSendReminders(c);

    if (result.remindersSent > 0) {
      log('info', `Sent ${result.remindersSent} expiry reminders`);
    }

    if (result.errors.length > 0) {
      log('warn', 'Reminder errors:', result.errors);
    }

    log('info', `Reminder check completed: ${result.remindersSent} reminders sent, ${result.errors.length} errors`);

  } catch (error) {
    log('error', 'Reminder check failed:', error);
  }
}
