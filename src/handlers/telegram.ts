import { Context } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { Env, TelegramUpdate, TelegramMessage } from '../types';
import { RegisterWebhookSchema, TelegramUpdateSchema } from '../schemas';
import { validateTelegramWebhook } from '../middleware/auth';
import { createDNSRecordFromBot, deleteDNSRecordFromBot, listUserDNSRecords, deleteAllUserDNSRecords } from '../services/dns';
import { logAuditEvent } from '../services/database';

export async function registerTelegramWebhook(c: Context<{ Bindings: Env }>) {
  try {
    const body = await c.req.json();
    const { webhook_url, secret_token } = RegisterWebhookSchema.parse(body);

    // Call Telegram API to set webhook
    const telegramApiUrl = `https://api.telegram.org/bot${c.env.TELEGRAM_BOT_TOKEN}/setWebhook`;

    const webhookData: any = {
      url: webhook_url,
      allowed_updates: ['message', 'callback_query'],
      drop_pending_updates: true,
    };

    if (secret_token) {
      webhookData.secret_token = secret_token;
    }

    const response = await fetch(telegramApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(webhookData),
    });

    const result = await response.json();

    if (!result.ok) {
      throw new Error(`Telegram API error: ${result.description}`);
    }

    // Store webhook URL in database
    await c.env.DB.prepare(
      'INSERT OR REPLACE INTO bot_settings (key, value) VALUES (?, ?)'
    ).bind('webhook_url', webhook_url).run();

    if (secret_token) {
      await c.env.DB.prepare(
        'INSERT OR REPLACE INTO bot_settings (key, value) VALUES (?, ?)'
      ).bind('webhook_secret_token', secret_token).run();
    }

    return c.json({
      success: true,
      message: 'Webhook registered successfully',
      webhook_info: result.result,
    });

  } catch (error) {
    console.error('Error registering webhook:', error);
    throw new HTTPException(500, {
      message: error instanceof Error ? error.message : 'Failed to register webhook',
    });
  }
}

export async function handleTelegramWebhook(c: Context<{ Bindings: Env }>) {
  try {
    // Get secret token from database for validation
    const secretTokenResult = await c.env.DB.prepare(
      'SELECT value FROM bot_settings WHERE key = ?'
    ).bind('webhook_secret_token').first();

    if (secretTokenResult) {
      await validateTelegramWebhook(c, secretTokenResult.value as string);
    }

    const body = await c.req.json();
    const update = TelegramUpdateSchema.parse(body);

    // Handle different types of updates
    if (update.message) {
      await handleMessage(c, update.message, update.update_id);
    } else if (update.callback_query) {
      await handleCallbackQuery(c, update.callback_query);
    }

    return c.json({ success: true, message: 'Update processed' });

  } catch (error) {
    console.error('Error handling webhook:', error);
    throw new HTTPException(400, {
      message: error instanceof Error ? error.message : 'Invalid update',
    });
  }
}

async function handleMessage(c: Context<{ Bindings: Env }>, message: TelegramMessage, updateId: number) {
  const chatId = message.chat.id;
  const userId = message.from?.id;
  const text = message.text?.trim();

  if (!userId || !text) {
    return;
  }

  // Store/update user info
  await storeUserInfo(c, message.from!, chatId);

  // Handle commands
  if (text.startsWith('/')) {
    await handleCommand(c, chatId, userId, text);
  } else {
    // Handle regular text (might be DNS record data)
    await handleTextMessage(c, chatId, userId, text);
  }
}

async function handleCommand(c: Context<{ Bindings: Env }>, chatId: number, userId: number, command: string) {
  const [cmd, ...args] = command.split(' ');
  const userRole = await getUserRole(c, userId);

  // Validate command
  if (!cmd) {
    return;
  }

  // Check if user has permission for this command
  if (!hasCommandPermission(userRole, cmd.toLowerCase())) {
    // Silent rejection for unauthorized users (no response)
    return;
  }

  switch (cmd.toLowerCase()) {
    case '/start':
      await sendMessage(c, chatId,
        'Welcome to DNS Bot! 🚀\n\n' +
        'Type /help to see available commands based on your permissions.'
      );
      break;

    case '/help':
      await handleHelpCommand(c, chatId, userRole);
      break;

    case '/help_old':
      await sendMessage(c, chatId,
        'DNS Bot Help 📖\n\n' +
        '🔹 /add <name> <type> <content> - Add DNS record\n' +
        '   Example: /add test.example.com A 192.168.1.1\n\n' +
        '🔹 /list - Show your DNS records\n' +
        '🔹 /delete <id> - Delete DNS record by ID\n' +
        '🔹 /deleteall - Delete ALL your DNS records ⚠️\n' +
        '🔹 /renew - Renew DNS expiry time (+24 hours)\n' +
        '🔹 /expiry - Check DNS expiry status\n' +
        '🔹 /reminder - Manage expiry reminders\n' +
        '🔹 /status - Show bot and service status\n\n' +
        'Supported DNS types: A, AAAA, CNAME, MX, TXT, SRV, NS'
      );
      break;

    case '/add':
      if (args.length < 3) {
        await sendMessage(c, chatId,
          'Usage: /add [name] [type] [content] [options]\n' +
          'Example: /add test.example.com A 192.168.1.1\n' +
          'Options: --proxy (force proxy), --no-proxy (disable proxy)'
        );
        return;
      }
      await handleAddDNSRecord(c, chatId, userId, args);
      break;

    case '/list':
      await handleListDNSRecords(c, chatId, userId);
      break;

    case '/delete':
      if (args.length < 1) {
        await sendMessage(c, chatId, 'Usage: /delete <record_id>');
        return;
      }
      await handleDeleteDNSRecord(c, chatId, userId, args[0]!);
      break;

    case '/deleteall':
      await handleDeleteAllDNSRecords(c, chatId, userId);
      break;

    case '/renew':
      await handleRenewExpiry(c, chatId, userId);
      break;

    case '/expiry':
      await handleCheckExpiry(c, chatId, userId);
      break;

    case '/reminder':
      await handleReminderCommand(c, chatId, userId, args);
      break;

    case '/status':
      await handleStatusCommand(c, chatId);
      break;

    case '/users':
      await handleUsersCommand(c, chatId, userId);
      break;

    case '/setrole':
      await handleSetRoleCommand(c, chatId, userId, args);
      break;

    case '/clearwebhook':
      await handleClearWebhookCommand(c, chatId, userId);
      break;

    default:
      await sendMessage(c, chatId, 'Unknown command. Type /help for available commands.');
  }
}

// User roles
enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

// Command definitions with permissions and descriptions
interface CommandConfig {
  command: string;
  requiredRole: UserRole;
  description: string;
  usage?: string;
  category: 'basic' | 'dns' | 'management' | 'admin';
  options?: string[];
}

// Permission configuration mapping
const COMMAND_PERMISSIONS: CommandConfig[] = [
  // Basic commands (available to all roles)
  {
    command: '/start',
    requiredRole: UserRole.USER,
    description: 'Start using the bot',
    category: 'basic'
  },
  {
    command: '/help',
    requiredRole: UserRole.USER,
    description: 'Show available commands',
    category: 'basic'
  },

  // DNS management commands (Admin+)
  {
    command: '/add',
    requiredRole: UserRole.SUPER_ADMIN,
    description: 'Add DNS record',
    usage: '/add [name] [type] [content] [options]',
    category: 'dns',
    options: ['--proxy (force proxy)', '--no-proxy (disable proxy)']
  },
  {
    command: '/list',
    requiredRole: UserRole.ADMIN,
    description: 'Show your DNS records',
    category: 'dns'
  },
  {
    command: '/delete',
    requiredRole: UserRole.ADMIN,
    description: 'Delete DNS record by ID',
    usage: '/delete [id]',
    category: 'dns'
  },
  {
    command: '/deleteall',
    requiredRole: UserRole.ADMIN,
    description: 'Delete ALL your DNS records ⚠️',
    category: 'dns'
  },

  // Expiry management commands (Admin+)
  {
    command: '/renew',
    requiredRole: UserRole.ADMIN,
    description: 'Renew DNS expiry time (+24 hours)',
    category: 'management'
  },
  {
    command: '/expiry',
    requiredRole: UserRole.ADMIN,
    description: 'Check DNS expiry status',
    category: 'management'
  },
  {
    command: '/reminder',
    requiredRole: UserRole.ADMIN,
    description: 'Manage expiry reminders',
    category: 'management'
  },
  {
    command: '/status',
    requiredRole: UserRole.ADMIN,
    description: 'Show bot and service status',
    category: 'management'
  },

  // Super Admin commands
  {
    command: '/users',
    requiredRole: UserRole.SUPER_ADMIN,
    description: 'List all users with their roles',
    category: 'admin'
  },
  {
    command: '/setrole',
    requiredRole: UserRole.SUPER_ADMIN,
    description: 'Set user role',
    usage: '/setrole [user_id] [role]',
    category: 'admin',
    options: ['Roles: user, admin, super_admin']
  },
  {
    command: '/clearwebhook',
    requiredRole: UserRole.SUPER_ADMIN,
    description: 'Clear webhook settings',
    category: 'admin'
  }
];

// Helper function to get command configuration
function getCommandConfig(command: string): CommandConfig | undefined {
  return COMMAND_PERMISSIONS.find(cmd => cmd.command === command);
}

// Helper function to check if user has permission for a command
function hasCommandPermission(userRole: UserRole, command: string): boolean {
  const config = getCommandConfig(command);
  if (!config) return false;
  return hasPermission(userRole, config.requiredRole);
}

// Helper function to get commands available to a role
function getAvailableCommands(userRole: UserRole): CommandConfig[] {
  return COMMAND_PERMISSIONS.filter(cmd => hasPermission(userRole, cmd.requiredRole));
}

// Helper function to determine if a record type should be proxied by default
function shouldEnableProxy(recordType: string): boolean {
  // Only A, AAAA, and CNAME records can be proxied
  const proxyableTypes = ['A', 'AAAA', 'CNAME'];
  return proxyableTypes.includes(recordType);
}

// Get user role from database
async function getUserRole(c: Context<{ Bindings: Env }>, userId: number): Promise<UserRole> {
  try {
    const user = await c.env.DB.prepare(
      'SELECT is_admin, role FROM users WHERE id = ?'
    ).bind(userId).first();

    if (!user) {
      return UserRole.USER;
    }

    // Check for role column first (new system)
    if (user.role) {
      return user.role as UserRole;
    }

    // Fallback to is_admin column (legacy)
    return user.is_admin ? UserRole.ADMIN : UserRole.USER;
  } catch (error) {
    console.error('Error getting user role:', error);
    return UserRole.USER;
  }
}

// Check if user has required permission
function hasPermission(userRole: UserRole, requiredRole: UserRole): boolean {
  const roleHierarchy = {
    [UserRole.USER]: 0,
    [UserRole.ADMIN]: 1,
    [UserRole.SUPER_ADMIN]: 2
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
}

// Handle help command based on user role
async function handleHelpCommand(c: Context<{ Bindings: Env }>, chatId: number, userRole: UserRole) {
  let helpMessage = 'DNS Bot Help 📖\n\n';

  if (userRole === UserRole.USER) {
    helpMessage += '❌ You do not have permission to use this bot.\n';
    helpMessage += 'Contact an administrator to get access.';
  } else {
    // Get available commands for this role
    const availableCommands = getAvailableCommands(userRole);

    // Group commands by category
    const categories = {
      basic: availableCommands.filter(cmd => cmd.category === 'basic'),
      dns: availableCommands.filter(cmd => cmd.category === 'dns'),
      management: availableCommands.filter(cmd => cmd.category === 'management'),
      admin: availableCommands.filter(cmd => cmd.category === 'admin')
    };

    helpMessage += `Available commands (${userRole === UserRole.SUPER_ADMIN ? 'Super Admin' : 'Admin'}):\n\n`;

    // DNS Management Commands
    if (categories.dns.length > 0) {
      helpMessage += '� DNS Management:\n';
      categories.dns.forEach(cmd => {
        helpMessage += `🔹 ${cmd.command}`;
        if (cmd.usage) {
          helpMessage += ` - ${cmd.description}\n   Usage: ${cmd.usage}\n`;
        } else {
          helpMessage += ` - ${cmd.description}\n`;
        }
        if (cmd.options) {
          cmd.options.forEach(option => {
            helpMessage += `   ${option}\n`;
          });
        }
      });
      helpMessage += '\n';
    }

    // Expiry Management Commands
    if (categories.management.length > 0) {
      helpMessage += '⏰ Expiry Management:\n';
      categories.management.forEach(cmd => {
        helpMessage += `🔹 ${cmd.command} - ${cmd.description}\n`;
      });
      helpMessage += '\n';
    }

    // Admin Commands
    if (categories.admin.length > 0) {
      helpMessage += '� Admin Commands:\n';
      categories.admin.forEach(cmd => {
        helpMessage += `🔹 ${cmd.command}`;
        if (cmd.usage) {
          helpMessage += ` - ${cmd.description}\n   Usage: ${cmd.usage}\n`;
        } else {
          helpMessage += ` - ${cmd.description}\n`;
        }
        if (cmd.options) {
          cmd.options.forEach(option => {
            helpMessage += `   ${option}\n`;
          });
        }
      });
      helpMessage += '\n';
    }

    // Additional info
    helpMessage += 'Supported DNS types: A, AAAA, CNAME, MX, TXT, SRV, NS\n\n';
    helpMessage += '🛡️ 代理模式: 流量通过Cloudflare (CDN/安全)\n';
    helpMessage += '📡 仅DNS模式: 直接连接源服务器';
  }

  await sendMessage(c, chatId, helpMessage);
}

// Handle users list command (Super Admin only)
async function handleUsersCommand(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    // Get all users from database
    const users = await c.env.DB.prepare(`
      SELECT id, username, first_name, last_name, role, created_at, last_active
      FROM users
      ORDER BY
        CASE role
          WHEN 'super_admin' THEN 1
          WHEN 'admin' THEN 2
          WHEN 'user' THEN 3
          ELSE 4
        END,
        last_active DESC
    `).all();

    if (!users.results || users.results.length === 0) {
      await sendMessage(c, chatId, '📝 No users found in the database.');
      return;
    }

    let message = `👥 User List (${users.results.length} users):\n\n`;

    // Group users by role
    const usersByRole = {
      super_admin: users.results.filter(u => u.role === 'super_admin'),
      admin: users.results.filter(u => u.role === 'admin'),
      user: users.results.filter(u => u.role === 'user' || !u.role)
    };

    // Super Admins
    if (usersByRole.super_admin.length > 0) {
      message += '👑 Super Admins:\n';
      usersByRole.super_admin.forEach((user: any) => {
        const name = user.first_name || user.username || 'Unknown';
        const lastActive = user.last_active ?
          new Date(user.last_active).toLocaleDateString() : 'Never';
        message += `  🆔 ${user.id} - ${name} (Last: ${lastActive})\n`;
      });
      message += '\n';
    }

    // Admins
    if (usersByRole.admin.length > 0) {
      message += '👨‍💼 Admins:\n';
      usersByRole.admin.forEach((user: any) => {
        const name = user.first_name || user.username || 'Unknown';
        const lastActive = user.last_active ?
          new Date(user.last_active).toLocaleDateString() : 'Never';
        message += `  🆔 ${user.id} - ${name} (Last: ${lastActive})\n`;
      });
      message += '\n';
    }

    // Regular Users
    if (usersByRole.user.length > 0) {
      message += '👤 Users:\n';
      usersByRole.user.forEach((user: any) => {
        const name = user.first_name || user.username || 'Unknown';
        const lastActive = user.last_active ?
          new Date(user.last_active).toLocaleDateString() : 'Never';
        message += `  🆔 ${user.id} - ${name} (Last: ${lastActive})\n`;
      });
      message += '\n';
    }

    message += '💡 Click buttons below to change user roles or use /setrole [user_id] [role]';

    // Create inline keyboard for role management
    const keyboard = {
      inline_keyboard: [] as any[]
    };

    // Add buttons for each user (limit to prevent message being too large)
    const allUsers = [...usersByRole.super_admin, ...usersByRole.admin, ...usersByRole.user];
    const maxUsersWithButtons = 10; // Limit to prevent keyboard being too large

    if (allUsers.length <= maxUsersWithButtons) {
      allUsers.forEach((user: any) => {
        const name = (user.first_name || user.username || 'Unknown').substring(0, 15);
        const currentRole = user.role || 'user';

        // Create role buttons for each user
        const userButtons = [];

        // Only show buttons for roles different from current role
        if (currentRole !== 'user') {
          userButtons.push({
            text: `👤 User`,
            callback_data: `setrole_${user.id}_user`
          });
        }

        if (currentRole !== 'admin') {
          userButtons.push({
            text: `👨‍💼 Admin`,
            callback_data: `setrole_${user.id}_admin`
          });
        }

        if (currentRole !== 'super_admin') {
          userButtons.push({
            text: `👑 Super Admin`,
            callback_data: `setrole_${user.id}_super_admin`
          });
        }

        // Add user info row
        if (userButtons.length > 0) {
          keyboard.inline_keyboard.push([{
            text: `${name} (${user.id})`,
            callback_data: `userinfo_${user.id}`
          }]);

          // Add role buttons row
          keyboard.inline_keyboard.push(userButtons);
        }
      });
    } else {
      // Too many users, just show refresh button
      keyboard.inline_keyboard.push([{
        text: '🔄 Refresh User List',
        callback_data: 'refresh_users'
      }]);
      message += '\n\n⚠️ Too many users to show buttons. Use /setrole command instead.';
    }

    // Add refresh button at the end
    if (keyboard.inline_keyboard.length > 0) {
      keyboard.inline_keyboard.push([{
        text: '🔄 Refresh List',
        callback_data: 'refresh_users'
      }]);
    }

    await sendMessage(c, chatId, message, keyboard.inline_keyboard.length > 0 ? keyboard : undefined);

    // Log the user list access
    await logAuditEvent(c, userId, 'VIEW_USER_LIST', 'user_management', 'user_list',
      `Viewed user list (${users.results.length} users)`);

  } catch (error) {
    console.error('Error listing users:', error);
    await sendMessage(c, chatId,
      `❌ Failed to retrieve user list: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

// Handle set role command (Super Admin only)
async function handleSetRoleCommand(c: Context<{ Bindings: Env }>, chatId: number, userId: number, args: string[]) {
  try {
    if (args.length < 2) {
      await sendMessage(c, chatId,
        'Usage: /setrole [user_id] [role]\n' +
        'Roles: user, admin, super_admin\n' +
        'Example: /setrole 123456789 admin'
      );
      return;
    }

    const targetUserId = parseInt(args[0]!);
    const newRole = args[1]!.toLowerCase();

    if (isNaN(targetUserId)) {
      await sendMessage(c, chatId, '❌ Invalid user ID. Must be a number.');
      return;
    }

    if (!Object.values(UserRole).includes(newRole as UserRole)) {
      await sendMessage(c, chatId, '❌ Invalid role. Use: user, admin, or super_admin');
      return;
    }

    // Update user role in database
    await c.env.DB.prepare(`
      INSERT OR REPLACE INTO users (id, role, last_active)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `).bind(targetUserId, newRole).run();

    // Log the role change
    await logAuditEvent(c, userId, 'SET_USER_ROLE', 'user_management', 'role_change',
      `Set role for user ${targetUserId} to ${newRole}`);

    await sendMessage(c, chatId,
      `✅ Role updated successfully!\n\n` +
      `👤 User ID: ${targetUserId}\n` +
      `🏷️ New Role: ${newRole}\n\n` +
      `The user will have ${newRole} permissions on their next interaction.`
    );

  } catch (error) {
    console.error('Error setting user role:', error);
    await sendMessage(c, chatId,
      `❌ Failed to set user role: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

// Handle clear webhook command (Super Admin only)
async function handleClearWebhookCommand(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    // Clear webhook_url from bot_settings
    await c.env.DB.prepare(`
      DELETE FROM bot_settings WHERE key = 'webhook_url'
    `).run();

    // Log the webhook clear
    await logAuditEvent(c, userId, 'CLEAR_WEBHOOK', 'system_management', 'webhook_clear',
      'Cleared webhook settings from database');

    await sendMessage(c, chatId,
      `✅ Webhook settings cleared!\n\n` +
      `The webhook URL has been removed from the database.\n` +
      `You can now register a new webhook using /telegram/register endpoint.`
    );

  } catch (error) {
    console.error('Error clearing webhook:', error);
    await sendMessage(c, chatId,
      `❌ Failed to clear webhook: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function handleAddDNSRecord(c: Context<{ Bindings: Env }>, chatId: number, userId: number, args: string[]) {
  try {
    const [name, type, content, ...extraArgs] = args;

    // Validate required parameters
    if (!name || !type || !content) {
      await sendMessage(c, chatId,
        'Usage: /add [name] [type] [content] [options]\n' +
        'Example: /add test.example.com A 192.168.1.1\n' +
        'Options: --proxy (force proxy), --no-proxy (disable proxy)'
      );
      return;
    }

    const recordType = type.toUpperCase();

    // Check if user specified proxy setting
    let proxied = shouldEnableProxy(recordType);

    // Check for proxy flag in extra args
    if (extraArgs.includes('--no-proxy') || extraArgs.includes('-np')) {
      proxied = false;
    } else if (extraArgs.includes('--proxy') || extraArgs.includes('-p')) {
      proxied = true;
    }

    const record = await createDNSRecordFromBot(c, {
      name,
      type: recordType,
      content,
      ttl: 300,
      proxied,
    }, userId, chatId);

    await logAuditEvent(c, userId, 'CREATE_DNS_RECORD', 'dns_record', record.id!,
      `Created DNS record: ${name} ${type} ${content}`);

    const proxyStatus = record.proxied ? '🟠 代理已启用' : '⚪ 仅DNS';
    const proxyEmoji = record.proxied ? '🛡️' : '📡';

    await sendMessage(c, chatId,
      `✅ DNS record created successfully!\n\n` +
      `📝 Name: ${record.name}\n` +
      `🏷️ Type: ${record.type}\n` +
      `📍 Content: ${record.content}\n` +
      `${proxyEmoji} Proxy: ${proxyStatus}\n` +
      `🆔 ID: ${record.id}\n\n` +
      `💡 提示: A/AAAA/CNAME记录默认启用代理\n` +
      `使用 --no-proxy 参数可禁用代理`
    );

  } catch (error) {
    console.error('Error adding DNS record:', error);
    await sendMessage(c, chatId,
      `❌ Failed to create DNS record: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function handleListDNSRecords(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    const records = await listUserDNSRecords(c, userId);

    if (records.length === 0) {
      await sendMessage(c, chatId, '📝 You have no DNS records yet. Use /add to create one.');
      return;
    }

    let message = `📋 Your DNS Records (${records.length}):\n\n`;

    records.forEach((record, index) => {
      const proxyEmoji = record.proxied ? '🛡️' : '📡';
      const proxyStatus = record.proxied ? '代理' : '仅DNS';

      message += `${index + 1}. ${record.name}\n`;
      message += `   🏷️ ${record.type} → ${record.content}\n`;
      message += `   ${proxyEmoji} ${proxyStatus}\n`;
      message += `   🆔 ID: ${record.id}\n`;
      message += `   📅 Created: ${record.created_on ? new Date(record.created_on).toLocaleDateString() : 'Unknown'}\n\n`;
    });

    await sendMessage(c, chatId, message);

  } catch (error) {
    console.error('Error listing DNS records:', error);
    await sendMessage(c, chatId, '❌ Failed to retrieve DNS records.');
  }
}

async function handleDeleteDNSRecord(c: Context<{ Bindings: Env }>, chatId: number, userId: number, recordId: string) {
  try {
    await deleteDNSRecordFromBot(c, recordId, userId);

    await logAuditEvent(c, userId, 'DELETE_DNS_RECORD', 'dns_record', recordId,
      `Deleted DNS record: ${recordId}`);

    await sendMessage(c, chatId, `✅ DNS record ${recordId} deleted successfully!`);

  } catch (error) {
    console.error('Error deleting DNS record:', error);
    await sendMessage(c, chatId,
      `❌ Failed to delete DNS record: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function handleDeleteAllDNSRecords(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    // First, get the count of records to delete
    const userRecords = await listUserDNSRecords(c, userId);

    if (userRecords.length === 0) {
      await sendMessage(c, chatId, '📝 You have no DNS records to delete.');
      return;
    }

    // Send confirmation message with inline keyboard
    const confirmationMessage = `⚠️ **DANGER ZONE** ⚠️\n\n` +
      `You are about to delete **ALL ${userRecords.length} DNS records**!\n\n` +
      `This action cannot be undone. Are you sure you want to proceed?`;

    const inlineKeyboard = {
      inline_keyboard: [
        [
          {
            text: '❌ Cancel',
            callback_data: 'cancel_delete_all'
          },
          {
            text: '🗑️ DELETE ALL',
            callback_data: 'confirm_delete_all'
          }
        ]
      ]
    };

    await sendMessage(c, chatId, confirmationMessage, inlineKeyboard);

  } catch (error) {
    console.error('Error preparing delete all DNS records:', error);
    await sendMessage(c, chatId,
      `❌ Failed to prepare deletion: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function executeDeleteAllDNSRecords(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    // Show processing message
    await sendMessage(c, chatId, '🔄 Deleting all DNS records... This may take a moment.');

    // Delete all records
    const result = await deleteAllUserDNSRecords(c, userId);

    // Log audit event
    await logAuditEvent(c, userId, 'DELETE_ALL_DNS_RECORDS', 'dns_records', 'all',
      `Deleted all DNS records: ${result.deleted} successful, ${result.errors.length} errors`);

    // Send result message
    let resultMessage = `✅ Deletion completed!\n\n`;
    resultMessage += `🗑️ Successfully deleted: ${result.deleted} records\n`;

    if (result.errors.length > 0) {
      resultMessage += `❌ Failed to delete: ${result.errors.length} records\n\n`;
      resultMessage += `Errors:\n`;
      result.errors.slice(0, 5).forEach((error, index) => {
        resultMessage += `${index + 1}. ${error}\n`;
      });

      if (result.errors.length > 5) {
        resultMessage += `... and ${result.errors.length - 5} more errors`;
      }
    }

    await sendMessage(c, chatId, resultMessage);

  } catch (error) {
    console.error('Error executing delete all DNS records:', error);
    await sendMessage(c, chatId,
      `❌ Failed to delete records: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function handleRenewExpiry(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    // Import DNS functions
    const { renewDNSExpiry, checkDNSExpiry } = await import('../services/dns');

    // Check current expiry status
    const currentStatus = await checkDNSExpiry(c);

    // Renew expiry time
    const newExpiryTime = await renewDNSExpiry(c);

    // Log audit event
    await logAuditEvent(c, userId, 'RENEW_DNS_EXPIRY', 'dns_expiry', 'renewal',
      `Renewed DNS expiry from ${currentStatus.expiryTime} to ${newExpiryTime}`);

    const renewalMessage = `✅ DNS 过期时间已续期！\n\n` +
      `🕐 新的过期时间: ${new Date(newExpiryTime).toLocaleString('zh-CN')}\n` +
      `⏰ 有效期: 24小时\n\n` +
      `💡 提示: 使用 /expiry 命令查看剩余时间`;

    await sendMessage(c, chatId, renewalMessage);

  } catch (error) {
    console.error('Error renewing DNS expiry:', error);
    await sendMessage(c, chatId,
      `❌ 续期失败: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function handleCheckExpiry(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    // Import DNS functions
    const { checkDNSExpiry } = await import('../services/dns');

    // Check expiry status
    const expiryStatus = await checkDNSExpiry(c);

    let statusMessage: string;

    if (!expiryStatus.expiryTime) {
      statusMessage = `⚠️ DNS 过期时间未设置\n\n` +
        `请使用 /renew 命令设置过期时间`;
    } else if (expiryStatus.isExpired) {
      statusMessage = `🚨 DNS 记录已过期！\n\n` +
        `📅 过期时间: ${new Date(expiryStatus.expiryTime).toLocaleString('zh-CN')}\n` +
        `⚠️ 所有DNS记录将在下次定时任务中被删除\n\n` +
        `🔄 使用 /renew 命令立即续期`;
    } else {
      statusMessage = `✅ DNS 记录有效\n\n` +
        `📅 过期时间: ${new Date(expiryStatus.expiryTime).toLocaleString('zh-CN')}\n` +
        `⏰ 剩余时间: ${expiryStatus.timeRemaining}\n\n` +
        `🔄 使用 /renew 命令续期 24 小时`;
    }

    await sendMessage(c, chatId, statusMessage);

  } catch (error) {
    console.error('Error checking DNS expiry:', error);
    await sendMessage(c, chatId,
      `❌ 查询失败: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function handleStatusCommand(c: Context<{ Bindings: Env }>, chatId: number) {
  try {
    // Import DNS functions
    const { checkDNSExpiry } = await import('../services/dns');

    // Check expiry status
    const expiryStatus = await checkDNSExpiry(c);

    let expiryInfo = '';
    if (expiryStatus.expiryTime) {
      if (expiryStatus.isExpired) {
        expiryInfo = `🚨 DNS已过期: ${new Date(expiryStatus.expiryTime).toLocaleString('zh-CN')}`;
      } else {
        expiryInfo = `✅ DNS有效期: ${expiryStatus.timeRemaining}`;
      }
    } else {
      expiryInfo = `⚠️ DNS过期时间未设置`;
    }

    const status = `🤖 DNS Bot Status\n\n` +
      `✅ Bot is running\n` +
      `🌐 Environment: ${c.env.ENVIRONMENT || 'unknown'}\n` +
      `⏰ Time: ${new Date().toLocaleString('zh-CN')}\n` +
      `🔧 Version: 1.0.0\n\n` +
      `📋 DNS Status:\n${expiryInfo}`;

    await sendMessage(c, chatId, status);
  } catch (error) {
    console.error('Error getting status:', error);
    const status = `🤖 DNS Bot Status\n\n` +
      `✅ Bot is running\n` +
      `🌐 Environment: ${c.env.ENVIRONMENT || 'unknown'}\n` +
      `⏰ Time: ${new Date().toLocaleString('zh-CN')}\n` +
      `🔧 Version: 1.0.0\n\n` +
      `❌ DNS状态查询失败`;

    await sendMessage(c, chatId, status);
  }
}

async function handleReminderCommand(c: Context<{ Bindings: Env }>, chatId: number, userId: number, args: string[]) {
  try {
    const subCommand = args[0]?.toLowerCase();

    switch (subCommand) {
      case 'status':
        await handleReminderStatus(c, chatId, userId);
        break;

      case 'test':
        await handleTestReminder(c, chatId, userId);
        break;

      case 'enable':
        await handleToggleReminder(c, chatId, userId, true);
        break;

      case 'disable':
        await handleToggleReminder(c, chatId, userId, false);
        break;

      default:
        await handleReminderHelp(c, chatId);
        break;
    }

  } catch (error) {
    console.error('Error handling reminder command:', error);
    await sendMessage(c, chatId,
      `❌ 提醒命令处理失败: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function handleReminderHelp(c: Context<{ Bindings: Env }>, chatId: number) {
  const helpMessage = `🔔 DNS过期提醒管理\n\n` +
    `📋 可用命令:\n` +
    `• /reminder status - 查看提醒状态\n` +
    `• /reminder test - 发送测试提醒\n` +
    `• /reminder enable - 启用提醒\n` +
    `• /reminder disable - 禁用提醒\n\n` +
    `ℹ️ 提醒说明:\n` +
    `• 在DNS过期前1小时开始提醒\n` +
    `• 每5分钟提醒一次\n` +
    `• 自动发送给所有活跃用户`;

  await sendMessage(c, chatId, helpMessage);
}

async function handleReminderStatus(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    const { getReminderStatus } = await import('../services/reminder');
    const { checkDNSExpiry } = await import('../services/dns');

    const reminderStatus = await getReminderStatus(c);
    const expiryStatus = await checkDNSExpiry(c);

    let statusMessage = `🔔 提醒状态\n\n`;

    statusMessage += `📊 提醒设置:\n`;
    statusMessage += `• 状态: ${reminderStatus.enabled ? '✅ 已启用' : '❌ 已禁用'}\n`;

    if (reminderStatus.lastReminderTime) {
      statusMessage += `• 上次提醒: ${new Date(reminderStatus.lastReminderTime).toLocaleString('zh-CN')}\n`;
    } else {
      statusMessage += `• 上次提醒: 从未发送\n`;
    }

    if (reminderStatus.nextCheckIn) {
      statusMessage += `• 下次检查: ${reminderStatus.nextCheckIn}\n`;
    }

    statusMessage += `\n📅 DNS状态:\n`;
    if (expiryStatus.expiryTime) {
      if (expiryStatus.isExpired) {
        statusMessage += `• 状态: 🚨 已过期\n`;
      } else {
        statusMessage += `• 状态: ✅ 有效\n`;
        statusMessage += `• 剩余时间: ${expiryStatus.timeRemaining}\n`;
      }
      statusMessage += `• 过期时间: ${new Date(expiryStatus.expiryTime).toLocaleString('zh-CN')}\n`;
    } else {
      statusMessage += `• 状态: ⚠️ 未设置过期时间\n`;
    }

    await sendMessage(c, chatId, statusMessage);

  } catch (error) {
    console.error('Error getting reminder status:', error);
    await sendMessage(c, chatId,
      `❌ 获取提醒状态失败: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function handleTestReminder(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    const { sendTestReminder } = await import('../services/reminder');

    await sendMessage(c, chatId, '🧪 正在发送测试提醒...');

    const success = await sendTestReminder(c, chatId);

    if (success) {
      await logAuditEvent(c, userId, 'TEST_REMINDER', 'reminder', 'test',
        `User ${userId} sent test reminder to chat ${chatId}`);
    } else {
      await sendMessage(c, chatId, '❌ 测试提醒发送失败');
    }

  } catch (error) {
    console.error('Error sending test reminder:', error);
    await sendMessage(c, chatId,
      `❌ 测试提醒失败: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function handleToggleReminder(c: Context<{ Bindings: Env }>, chatId: number, userId: number, enabled: boolean) {
  try {
    const { toggleReminders } = await import('../services/reminder');

    await toggleReminders(c, enabled);

    const statusText = enabled ? '启用' : '禁用';
    const statusEmoji = enabled ? '✅' : '❌';

    await logAuditEvent(c, userId, 'TOGGLE_REMINDER', 'reminder', 'toggle',
      `User ${userId} ${statusText} reminders`);

    await sendMessage(c, chatId,
      `${statusEmoji} 提醒功能已${statusText}\n\n` +
      `${enabled ?
        '🔔 现在会在DNS过期前1小时开始提醒\n每5分钟提醒一次' :
        '🔕 不再发送过期提醒'}`
    );

  } catch (error) {
    console.error('Error toggling reminders:', error);
    await sendMessage(c, chatId,
      `❌ 设置提醒失败: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function handleTextMessage(c: Context<{ Bindings: Env }>, chatId: number, userId: number, text: string) {
  // For now, just show help for non-command messages
  await sendMessage(c, chatId, 'Please use commands to interact with the bot. Type /help for available commands.');
}

async function handleCallbackQuery(c: Context<{ Bindings: Env }>, callbackQuery: any) {
  // Handle inline keyboard callbacks
  let chatId = callbackQuery.message?.chat?.id;
  const userId = callbackQuery.from.id;
  const data = callbackQuery.data;

  console.log('Callback query received:', {
    chatId,
    userId,
    data,
    hasMessage: !!callbackQuery.message,
    hasChat: !!callbackQuery.message?.chat,
    messageType: typeof callbackQuery.message
  });

  // If chatId is not available from message, try to get it from database
  if (!chatId && userId) {
    console.log('ChatId not found in callback, looking up in database for userId:', userId);
    try {
      const userRecord = await c.env.DB.prepare(
        'SELECT chat_id FROM users WHERE id = ?'
      ).bind(userId).first();

      if (userRecord?.chat_id) {
        chatId = userRecord.chat_id as number;
        console.log('Found chatId in database:', chatId);
      }
    } catch (error) {
      console.error('Error looking up chatId:', error);
    }
  }

  if (!chatId || !data) {
    console.log('Missing chatId or data after lookup:', { chatId, data, userId });
    return;
  }

  // Answer the callback query
  await fetch(`https://api.telegram.org/bot${c.env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      callback_query_id: callbackQuery.id,
      text: 'Processing...',
    }),
  });

  // Get user role for permission checking
  const userRole = await getUserRole(c, userId);

  // Handle different callback data with permission checks
  if (data.startsWith('delete_')) {
    console.log('Handling delete record callback');
    if (!hasPermission(userRole, UserRole.ADMIN)) {
      await sendMessage(c, chatId, '❌ You do not have permission to delete DNS records.');
      return;
    }
    const recordId = data.replace('delete_', '');
    await handleDeleteDNSRecord(c, chatId, userId, recordId);
  } else if (data === 'confirm_delete_all') {
    console.log('Handling confirm delete all callback');
    if (!hasPermission(userRole, UserRole.ADMIN)) {
      await sendMessage(c, chatId, '❌ You do not have permission to delete DNS records.');
      return;
    }
    await executeDeleteAllDNSRecords(c, chatId, userId);
  } else if (data === 'cancel_delete_all') {
    console.log('Handling cancel delete all callback');
    if (!hasPermission(userRole, UserRole.ADMIN)) {
      await sendMessage(c, chatId, '❌ You do not have permission to perform this action.');
      return;
    }
    await sendMessage(c, chatId, '✅ Deletion cancelled. Your DNS records are safe.');
  } else if (data.startsWith('setrole_')) {
    console.log('Handling set role callback');
    if (!hasPermission(userRole, UserRole.SUPER_ADMIN)) {
      await sendMessage(c, chatId, '❌ You do not have permission to change user roles.');
      return;
    }
    await handleSetRoleCallback(c, chatId, userId, data);
  } else if (data === 'refresh_users') {
    console.log('Handling refresh users callback');
    if (!hasPermission(userRole, UserRole.SUPER_ADMIN)) {
      await sendMessage(c, chatId, '❌ You do not have permission to view user list.');
      return;
    }
    await handleUsersCommand(c, chatId, userId);
  } else if (data.startsWith('userinfo_')) {
    console.log('Handling user info callback');
    if (!hasPermission(userRole, UserRole.SUPER_ADMIN)) {
      await sendMessage(c, chatId, '❌ You do not have permission to view user details.');
      return;
    }
    const targetUserId = data.replace('userinfo_', '');
    await handleUserInfoCallback(c, chatId, userId, targetUserId);
  } else {
    console.log('Unknown callback data:', data);
  }
}

async function storeUserInfo(c: Context<{ Bindings: Env }>, user: any, chatId?: number) {
  try {
    // First, try to update existing user without changing role
    const updateResult = await c.env.DB.prepare(`
      UPDATE users
      SET username = ?, first_name = ?, last_name = ?, chat_id = ?, last_active = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(
      user.username || null,
      user.first_name,
      user.last_name || null,
      chatId || null,
      user.id
    ).run();

    // If no rows were updated, the user doesn't exist, so insert new user
    if (updateResult.meta.changes === 0) {
      await c.env.DB.prepare(`
        INSERT INTO users (id, username, first_name, last_name, chat_id, role, last_active)
        VALUES (?, ?, ?, ?, ?, 'user', CURRENT_TIMESTAMP)
      `).bind(
        user.id,
        user.username || null,
        user.first_name,
        user.last_name || null,
        chatId || null
      ).run();
    }
  } catch (error) {
    console.error('Error storing user info:', error);
  }
}

// Handle set role callback from inline button
async function handleSetRoleCallback(c: Context<{ Bindings: Env }>, chatId: number, userId: number, data: string) {
  try {
    // Permission already checked in handleCallbackQuery

    // Parse callback data: setrole_[target_user_id]_[new_role]
    // Handle super_admin role which contains underscore
    const match = data.match(/^setrole_(\d+)_(.+)$/);
    if (!match) {
      await sendMessage(c, chatId, '❌ Invalid callback data format.');
      return;
    }

    const targetUserId = parseInt(match[1]!);
    const newRole = match[2]!;

    if (isNaN(targetUserId)) {
      await sendMessage(c, chatId, '❌ Invalid user ID.');
      return;
    }

    if (!Object.values(UserRole).includes(newRole as UserRole)) {
      await sendMessage(c, chatId, '❌ Invalid role.');
      return;
    }

    // Get target user info for confirmation
    const targetUser = await c.env.DB.prepare(
      'SELECT id, username, first_name, role FROM users WHERE id = ?'
    ).bind(targetUserId).first();

    if (!targetUser) {
      await sendMessage(c, chatId, '❌ Target user not found.');
      return;
    }

    // Update user role in database
    await c.env.DB.prepare(`
      UPDATE users SET role = ? WHERE id = ?
    `).bind(newRole, targetUserId).run();

    // Log the role change
    await logAuditEvent(c, userId, 'SET_USER_ROLE', 'user_management', 'role_change',
      `Set role for user ${targetUserId} to ${newRole} via button`);

    const targetName = targetUser.first_name || targetUser.username || 'Unknown';
    const oldRole = targetUser.role || 'user';

    await sendMessage(c, chatId,
      `✅ Role updated successfully!\n\n` +
      `👤 User: ${targetName} (${targetUserId})\n` +
      `🔄 Changed: ${oldRole} → ${newRole}\n\n` +
      `The user will have ${newRole} permissions on their next interaction.`
    );

    // Refresh the user list to show updated roles
    setTimeout(() => {
      handleUsersCommand(c, chatId, userId);
    }, 1000);

  } catch (error) {
    console.error('Error handling set role callback:', error);
    await sendMessage(c, chatId,
      `❌ Failed to update user role: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

// Handle user info callback from inline button
async function handleUserInfoCallback(c: Context<{ Bindings: Env }>, chatId: number, userId: number, targetUserId: string) {
  try {
    // Permission already checked in handleCallbackQuery

    const targetUserIdNum = parseInt(targetUserId);
    if (isNaN(targetUserIdNum)) {
      await sendMessage(c, chatId, '❌ Invalid user ID.');
      return;
    }

    // Get detailed user info
    const user = await c.env.DB.prepare(`
      SELECT id, username, first_name, last_name, role, created_at, last_active
      FROM users WHERE id = ?
    `).bind(targetUserIdNum).first();

    if (!user) {
      await sendMessage(c, chatId, '❌ User not found.');
      return;
    }

    const fullName = [user.first_name, user.last_name].filter(Boolean).join(' ') || 'Not provided';
    const username = user.username || 'Not set';
    const role = (user.role as string) || 'user';
    const created = user.created_at ? new Date(user.created_at as string).toLocaleString() : 'Unknown';
    const lastActive = user.last_active ? new Date(user.last_active as string).toLocaleString() : 'Never';

    const roleEmojiMap: Record<string, string> = {
      'super_admin': '👑',
      'admin': '👨‍💼',
      'user': '👤'
    };
    const roleEmoji = roleEmojiMap[role] || '👤';

    await sendMessage(c, chatId,
      `👤 User Details\n\n` +
      `🆔 ID: ${user.id}\n` +
      `📝 Name: ${fullName}\n` +
      `🏷️ Username: @${username}\n` +
      `${roleEmoji} Role: ${role}\n` +
      `📅 Created: ${created}\n` +
      `⏰ Last Active: ${lastActive}\n\n` +
      `Use /setrole ${user.id} [role] to change their role.`
    );

  } catch (error) {
    console.error('Error handling user info callback:', error);
    await sendMessage(c, chatId,
      `❌ Failed to get user info: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function sendMessage(c: Context<{ Bindings: Env }>, chatId: number, text: string, replyMarkup?: any) {
  const payload: any = {
    chat_id: chatId,
    text,
    // parse_mode: 'HTML', // Disabled to avoid HTML parsing issues
  };

  if (replyMarkup) {
    payload.reply_markup = replyMarkup;
  }

  const response = await fetch(`https://api.telegram.org/bot${c.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const error = await response.text();
    console.error('Failed to send message:', error);
    throw new Error(`Telegram API error: ${error}`);
  }

  return response.json();
}
