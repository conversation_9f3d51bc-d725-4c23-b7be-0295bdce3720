import { Context } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { Env, TelegramUpdate, TelegramMessage } from '../types';
import { RegisterWebhookSchema, TelegramUpdateSchema } from '../schemas';
import { validateTelegramWebhook } from '../middleware/auth';
import { createDNSRecordFromBot, deleteDNSRecordFromBot, listUserDNSRecords, deleteAllUserDNSRecords } from '../services/dns';
import { logAuditEvent } from '../services/database';
import {
  UserRole,
  PermissionUtils,
  TimeUtils,
  MESSAGES,
  DNS_TYPES,
  APP_LIMITS
} from '../config';

export async function registerTelegramWebhook(c: Context<{ Bindings: Env }>) {
  try {
    const body = await c.req.json();
    const { webhook_url, secret_token } = RegisterWebhookSchema.parse(body);

    // Call Telegram API to set webhook
    const telegramApiUrl = `https://api.telegram.org/bot${c.env.TELEGRAM_BOT_TOKEN}/setWebhook`;

    const webhookData: any = {
      url: webhook_url,
      allowed_updates: ['message', 'callback_query'],
      drop_pending_updates: true,
    };

    if (secret_token) {
      webhookData.secret_token = secret_token;
    }

    const response = await fetch(telegramApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(webhookData),
    });

    const result = await response.json();

    if (!result.ok) {
      throw new Error(`Telegram API error: ${result.description}`);
    }

    // Store webhook URL in database
    await c.env.DB.prepare(
      'INSERT OR REPLACE INTO bot_settings (key, value) VALUES (?, ?)'
    ).bind('webhook_url', webhook_url).run();

    if (secret_token) {
      await c.env.DB.prepare(
        'INSERT OR REPLACE INTO bot_settings (key, value) VALUES (?, ?)'
      ).bind('webhook_secret_token', secret_token).run();
    }

    return c.json({
      success: true,
      message: 'Webhook注册成功',
      webhook_info: result.result,
    });

  } catch (error) {
    console.error('Error registering webhook:', error);
    throw new HTTPException(500, {
      message: error instanceof Error ? error.message : 'Webhook注册失败',
    });
  }
}

export async function handleTelegramWebhook(c: Context<{ Bindings: Env }>) {
  try {
    // Get secret token from database for validation
    const secretTokenResult = await c.env.DB.prepare(
      'SELECT value FROM bot_settings WHERE key = ?'
    ).bind('webhook_secret_token').first();

    if (secretTokenResult) {
      await validateTelegramWebhook(c, secretTokenResult.value as string);
    }

    const body = await c.req.json();
    const update = TelegramUpdateSchema.parse(body);

    // Handle different types of updates
    if (update.message) {
      await handleMessage(c, update.message, update.update_id);
    } else if (update.callback_query) {
      await handleCallbackQuery(c, update.callback_query);
    }

    return c.json({ success: true, message: '更新已处理' });

  } catch (error) {
    console.error('Error handling webhook:', error);
    throw new HTTPException(400, {
      message: error instanceof Error ? error.message : '无效的更新',
    });
  }
}

async function handleMessage(c: Context<{ Bindings: Env }>, message: TelegramMessage, updateId: number) {
  const chatId = message.chat.id;
  const userId = message.from?.id;
  const text = message.text?.trim();

  if (!userId || !text) {
    return;
  }

  // Store/update user info
  await storeUserInfo(c, message.from!, chatId);

  // Handle commands
  if (text.startsWith('/')) {
    await handleCommand(c, chatId, userId, text);
  } else {
    // Handle regular text (might be DNS record data)
    await handleTextMessage(c, chatId, userId, text);
  }
}

async function handleCommand(c: Context<{ Bindings: Env }>, chatId: number, userId: number, command: string) {
  const [cmd, ...args] = command.split(' ');
  const userRole = await getUserRole(c, userId);

  // Validate command
  if (!cmd) {
    return;
  }

  // Check if user has permission for this command
  if (!PermissionUtils.hasCommandPermission(userRole, cmd.toLowerCase())) {
    // Silent rejection for unauthorized users (no response)
    return;
  }

  switch (cmd.toLowerCase()) {
    case '/start':
      await sendMessage(c, chatId,
        '欢迎使用 DNS Bot! 🚀\n\n' +
        '输入 /help 查看您可用的命令。'
      );
      break;

    case '/help':
      await handleHelpCommand(c, chatId, userRole);
      break;


    case '/add':
      if (args.length < 3) {
        await sendMessage(c, chatId,
          '用法: /add [域名] [类型] [内容] [选项]\n' +
          '示例: /add test.example.com A 192.168.1.1\n' +
          '选项: --proxy (强制启用代理), --no-proxy (禁用代理)'
        );
        return;
      }
      await handleAddDNSRecord(c, chatId, userId, args);
      break;

    case '/list':
      await handleListDNSRecords(c, chatId, userId);
      break;

    case '/delete':
      if (args.length < 1) {
        await sendMessage(c, chatId, '用法: /delete <记录ID>');
        return;
      }
      await handleDeleteDNSRecord(c, chatId, userId, args[0]!);
      break;

    case '/deleteall':
      await handleDeleteAllDNSRecords(c, chatId, userId);
      break;

    case '/renew':
      await handleRenewExpiry(c, chatId, userId);
      break;

    case '/expiry':
      await handleCheckExpiry(c, chatId, userId);
      break;

    case '/reminder':
      await handleReminderCommand(c, chatId, userId, args);
      break;

    case '/status':
      await handleStatusCommand(c, chatId);
      break;

    case '/users':
      await handleUsersCommand(c, chatId, userId);
      break;

    case '/setrole':
      await handleSetRoleCommand(c, chatId, userId, args);
      break;

    case '/clearwebhook':
      await handleClearWebhookCommand(c, chatId, userId);
      break;

    default:
      await sendMessage(c, chatId, '未知命令。输入 /help 查看可用命令。');
  }
}

// UserRole 现在从配置模块导入

// 权限配置现在从配置模块导入，避免重复定义

// 这些函数现在使用配置模块中的PermissionUtils

// 回调权限配置现在从配置模块导入

// Helper function to determine if a record type should be proxied by default
function shouldEnableProxy(recordType: string): boolean {
  // Only A, AAAA, and CNAME records can be proxied
  const proxyableTypes = ['A', 'AAAA', 'CNAME'];
  return proxyableTypes.includes(recordType);
}

// Get user role from database
async function getUserRole(c: Context<{ Bindings: Env }>, userId: number): Promise<UserRole> {
  try {
    const user = await c.env.DB.prepare(
      'SELECT is_admin, role FROM users WHERE id = ?'
    ).bind(userId).first();

    if (!user) {
      return UserRole.USER;
    }

    // Check for role column first (new system)
    if (user.role) {
      return user.role as UserRole;
    }

    // Fallback to is_admin column (legacy)
    return user.is_admin ? UserRole.ADMIN : UserRole.USER;
  } catch (error) {
    console.error('Error getting user role:', error);
    return UserRole.USER;
  }
}

// hasPermission 函数现在从 PermissionUtils 导入

// Handle help command based on user role
async function handleHelpCommand(c: Context<{ Bindings: Env }>, chatId: number, userRole: UserRole) {
  let helpMessage = 'DNS Bot 帮助 📖\n\n';

  if (userRole === UserRole.USER) {
    helpMessage += '❌ 您没有权限使用此机器人。\n';
    helpMessage += '请联系管理员获取访问权限。';
  } else {
    // Get available commands for this role
    const availableCommands = PermissionUtils.getAvailableCommands(userRole);

    // Group commands by category
    const categories = {
      basic: availableCommands.filter(cmd => cmd.category === 'basic'),
      dns: availableCommands.filter(cmd => cmd.category === 'dns'),
      management: availableCommands.filter(cmd => cmd.category === 'management'),
      admin: availableCommands.filter(cmd => cmd.category === 'admin')
    };

    helpMessage += `可用命令 (${userRole === UserRole.SUPER_ADMIN ? '超级管理员' : '管理员'}):\n\n`;

    // DNS Management Commands
    if (categories.dns.length > 0) {
      helpMessage += '� DNS Management:\n';
      categories.dns.forEach(cmd => {
        helpMessage += `🔹 ${cmd.command}`;
        if (cmd.usage) {
          helpMessage += ` - ${cmd.description}\n   Usage: ${cmd.usage}\n`;
        } else {
          helpMessage += ` - ${cmd.description}\n`;
        }
        if (cmd.options) {
          cmd.options.forEach(option => {
            helpMessage += `   ${option}\n`;
          });
        }
      });
      helpMessage += '\n';
    }

    // Expiry Management Commands
    if (categories.management.length > 0) {
      helpMessage += '⏰ 过期管理:\n';
      categories.management.forEach(cmd => {
        helpMessage += `🔹 ${cmd.command} - ${cmd.description}\n`;
      });
      helpMessage += '\n';
    }

    // Admin Commands
    if (categories.admin.length > 0) {
      helpMessage += '� Admin Commands:\n';
      categories.admin.forEach(cmd => {
        helpMessage += `🔹 ${cmd.command}`;
        if (cmd.usage) {
          helpMessage += ` - ${cmd.description}\n   Usage: ${cmd.usage}\n`;
        } else {
          helpMessage += ` - ${cmd.description}\n`;
        }
        if (cmd.options) {
          cmd.options.forEach(option => {
            helpMessage += `   ${option}\n`;
          });
        }
      });
      helpMessage += '\n';
    }

    // Additional info
    helpMessage += '支持的DNS类型: A, AAAA, CNAME, MX, TXT, SRV, NS\n\n';
    helpMessage += '🛡️ 代理模式: 流量通过Cloudflare (CDN/安全)\n';
    helpMessage += '📡 仅DNS模式: 直接连接源服务器';
  }

  await sendMessage(c, chatId, helpMessage);
}

// Handle users list command (Super Admin only)
async function handleUsersCommand(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    // Get all users from database
    const users = await c.env.DB.prepare(`
      SELECT id, username, first_name, last_name, role, created_at, last_active
      FROM users
      ORDER BY
        CASE role
          WHEN 'super_admin' THEN 1
          WHEN 'admin' THEN 2
          WHEN 'user' THEN 3
          ELSE 4
        END,
        last_active DESC
    `).all();

    if (!users.results || users.results.length === 0) {
      await sendMessage(c, chatId, '📝 数据库中暂无用户。');
      return;
    }

    let message = `👥 用户列表 (${users.results.length} 个用户):\n\n`;

    // Group users by role
    const usersByRole = {
      super_admin: users.results.filter(u => u.role === 'super_admin'),
      admin: users.results.filter(u => u.role === 'admin'),
      user: users.results.filter(u => u.role === 'user' || !u.role)
    };

    // Super Admins
    if (usersByRole.super_admin.length > 0) {
      message += '👑 超级管理员:\n';
      usersByRole.super_admin.forEach((user: any) => {
        const name = user.first_name || user.username || '未知用户';
        const lastActive = user.last_active ?
          TimeUtils.getRelativeTime(user.last_active) : MESSAGES.TIME.NEVER;
        message += `  🆔 ${user.id} - ${name} (最后活跃: ${lastActive})\n`;
      });
      message += '\n';
    }

    // Admins
    if (usersByRole.admin.length > 0) {
      message += '👨‍💼 管理员:\n';
      usersByRole.admin.forEach((user: any) => {
        const name = user.first_name || user.username || '未知用户';
        const lastActive = user.last_active ?
          TimeUtils.getRelativeTime(user.last_active) : MESSAGES.TIME.NEVER;
        message += `  🆔 ${user.id} - ${name} (最后活跃: ${lastActive})\n`;
      });
      message += '\n';
    }

    // Regular Users
    if (usersByRole.user.length > 0) {
      message += '👤 普通用户:\n';
      usersByRole.user.forEach((user: any) => {
        const name = user.first_name || user.username || '未知用户';
        const lastActive = user.last_active ?
          TimeUtils.getRelativeTime(user.last_active) : MESSAGES.TIME.NEVER;
        message += `  🆔 ${user.id} - ${name} (最后活跃: ${lastActive})\n`;
      });
      message += '\n';
    }

    message += '💡 点击下方按钮更改用户角色，或使用 /setrole [用户ID] [角色]';

    // Create inline keyboard for role management
    const keyboard = {
      inline_keyboard: [] as any[]
    };

    // Add buttons for each user (limit to prevent message being too large)
    const allUsers = [...usersByRole.super_admin, ...usersByRole.admin, ...usersByRole.user];
    const maxUsersWithButtons = 10; // Limit to prevent keyboard being too large

    if (allUsers.length <= maxUsersWithButtons) {
      allUsers.forEach((user: any) => {
        const name = (user.first_name || user.username || '未知用户').substring(0, 15);
        const currentRole = user.role || 'user';

        // Create role buttons for each user
        const userButtons = [];

        // Only show buttons for roles different from current role
        if (currentRole !== 'user') {
          userButtons.push({
            text: `👤 User`,
            callback_data: `setrole_${user.id}_user`
          });
        }

        if (currentRole !== 'admin') {
          userButtons.push({
            text: `👨‍💼 Admin`,
            callback_data: `setrole_${user.id}_admin`
          });
        }

        if (currentRole !== 'super_admin') {
          userButtons.push({
            text: `👑 Super Admin`,
            callback_data: `setrole_${user.id}_super_admin`
          });
        }

        // Add user info row
        if (userButtons.length > 0) {
          keyboard.inline_keyboard.push([{
            text: `${name} (${user.id})`,
            callback_data: `userinfo_${user.id}`
          }]);

          // Add role buttons row
          keyboard.inline_keyboard.push(userButtons);
        }
      });
    } else {
      // Too many users, just show refresh button
      keyboard.inline_keyboard.push([{
        text: '🔄 刷新用户列表',
        callback_data: 'refresh_users'
      }]);
      message += '\n\n⚠️ 用户过多，无法显示按钮。请使用 /setrole 命令。';
    }

    // Add refresh button at the end
    if (keyboard.inline_keyboard.length > 0) {
      keyboard.inline_keyboard.push([{
        text: '🔄 刷新列表',
        callback_data: 'refresh_users'
      }]);
    }

    await sendMessage(c, chatId, message, keyboard.inline_keyboard.length > 0 ? keyboard : undefined);

    // Log the user list access
    await logAuditEvent(c, userId, 'VIEW_USER_LIST', 'user_management', 'user_list',
      `Viewed user list (${users.results.length} users)`);

  } catch (error) {
    console.error('Error listing users:', error);
    await sendMessage(c, chatId,
      `❌ 获取用户列表失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

// Handle set role command (Super Admin only)
async function handleSetRoleCommand(c: Context<{ Bindings: Env }>, chatId: number, userId: number, args: string[]) {
  try {
    if (args.length < 2) {
      await sendMessage(c, chatId,
        '用法: /setrole [用户ID] [角色]\n' +
        '角色: user, admin, super_admin\n' +
        '示例: /setrole 123456789 admin'
      );
      return;
    }

    const targetUserId = parseInt(args[0]!);
    const newRole = args[1]!.toLowerCase();

    if (isNaN(targetUserId)) {
      await sendMessage(c, chatId, '❌ 无效的用户ID。必须是数字。');
      return;
    }

    if (!Object.values(UserRole).includes(newRole as UserRole)) {
      await sendMessage(c, chatId, '❌ 无效的角色。请使用: user, admin, 或 super_admin');
      return;
    }

    // Update user role in database
    await c.env.DB.prepare(`
      INSERT OR REPLACE INTO users (id, role, last_active)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `).bind(targetUserId, newRole).run();

    // Log the role change
    await logAuditEvent(c, userId, 'SET_USER_ROLE', 'user_management', 'role_change',
      `Set role for user ${targetUserId} to ${newRole}`);

    await sendMessage(c, chatId,
      `✅ 用户角色更新成功!\n\n` +
      `👤 用户ID: ${targetUserId}\n` +
      `🏷️ 新角色: ${PermissionUtils.getRoleDisplayName(newRole as UserRole)}\n\n` +
      `该用户在下次交互时将拥有${PermissionUtils.getRoleDisplayName(newRole as UserRole)}权限。`
    );

  } catch (error) {
    console.error('Error setting user role:', error);
    await sendMessage(c, chatId,
      `❌ 设置用户角色失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

// Handle clear webhook command (Super Admin only)
async function handleClearWebhookCommand(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    // Clear webhook_url from bot_settings
    await c.env.DB.prepare(`
      DELETE FROM bot_settings WHERE key = 'webhook_url'
    `).run();

    // Log the webhook clear
    await logAuditEvent(c, userId, 'CLEAR_WEBHOOK', 'system_management', 'webhook_clear',
      'Cleared webhook settings from database');

    await sendMessage(c, chatId,
      `✅ Webhook设置已清除!\n\n` +
      `Webhook URL已从数据库中移除。\n` +
      `您现在可以使用 /telegram/register 端点注册新的webhook。`
    );

  } catch (error) {
    console.error('Error clearing webhook:', error);
    await sendMessage(c, chatId,
      `❌ 清除webhook失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

async function handleAddDNSRecord(c: Context<{ Bindings: Env }>, chatId: number, userId: number, args: string[]) {
  try {
    const [name, type, content, ...extraArgs] = args;

    // Validate required parameters
    if (!name || !type || !content) {
      await sendMessage(c, chatId,
        '用法: /add [域名] [类型] [内容] [选项]\n' +
        '示例: /add test.example.com A 192.168.1.1\n' +
        '选项: --proxy (强制启用代理), --no-proxy (禁用代理)'
      );
      return;
    }

    const recordType = type.toUpperCase();

    // Check if user specified proxy setting
    let proxied = shouldEnableProxy(recordType);

    // Check for proxy flag in extra args
    if (extraArgs.includes('--no-proxy') || extraArgs.includes('-np')) {
      proxied = false;
    } else if (extraArgs.includes('--proxy') || extraArgs.includes('-p')) {
      proxied = true;
    }

    const record = await createDNSRecordFromBot(c, {
      name,
      type: recordType,
      content,
      ttl: 300,
      proxied,
    }, userId, chatId);

    await logAuditEvent(c, userId, 'CREATE_DNS_RECORD', 'dns_record', record.id!,
      `Created DNS record: ${name} ${type} ${content}`);

    const proxyStatus = record.proxied ? '🟠 代理已启用' : '⚪ 仅DNS';
    const proxyEmoji = record.proxied ? '🛡️' : '📡';

    await sendMessage(c, chatId,
      `✅ DNS记录创建成功!\n\n` +
      `📝 域名: ${record.name}\n` +
      `🏷️ 类型: ${record.type}\n` +
      `📍 内容: ${record.content}\n` +
      `${proxyEmoji} 代理: ${proxyStatus}\n` +
      `🆔 ID: ${record.id}\n\n` +
      `💡 提示: A/AAAA/CNAME记录默认启用代理\n` +
      `使用 --no-proxy 参数可禁用代理`
    );

  } catch (error) {
    console.error('Error adding DNS record:', error);
    await sendMessage(c, chatId,
      `❌ 创建DNS记录失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

async function handleListDNSRecords(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    const records = await listUserDNSRecords(c, userId);

    if (records.length === 0) {
      await sendMessage(c, chatId, '📝 您还没有DNS记录。使用 /add 创建一个。');
      return;
    }

    let message = `📋 您的DNS记录 (${records.length}条):\n\n`;

    records.forEach((record, index) => {
      const proxyEmoji = record.proxied ? '🛡️' : '📡';
      const proxyStatus = record.proxied ? '代理' : '仅DNS';

      message += `${index + 1}. ${record.name}\n`;
      message += `   🏷️ ${record.type} → ${record.content}\n`;
      message += `   ${proxyEmoji} ${proxyStatus}\n`;
      message += `   🆔 ID: ${record.id}\n`;
      message += `   📅 创建时间: ${record.created_on ? TimeUtils.formatBeijingTime(record.created_on, 'short') : MESSAGES.TIME.UNKNOWN}\n\n`;
    });

    await sendMessage(c, chatId, message);

  } catch (error) {
    console.error('Error listing DNS records:', error);
    await sendMessage(c, chatId, '❌ 获取DNS记录失败。');
  }
}

async function handleDeleteDNSRecord(c: Context<{ Bindings: Env }>, chatId: number, userId: number, recordId: string) {
  try {
    await deleteDNSRecordFromBot(c, recordId, userId);

    await logAuditEvent(c, userId, 'DELETE_DNS_RECORD', 'dns_record', recordId,
      `Deleted DNS record: ${recordId}`);

    await sendMessage(c, chatId, `✅ DNS记录 ${recordId} 删除成功!`);

  } catch (error) {
    console.error('Error deleting DNS record:', error);
    await sendMessage(c, chatId,
      `❌ 删除DNS记录失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

async function handleDeleteAllDNSRecords(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    // First, get the count of records to delete
    const userRecords = await listUserDNSRecords(c, userId);

    if (userRecords.length === 0) {
      await sendMessage(c, chatId, '📝 您还没有DNS记录。');
      return;
    }

    // Send confirmation message with inline keyboard
    const confirmationMessage = `⚠️ **危险操作** ⚠️\n\n` +
      `您正在删除**所有 ${userRecords.length} 条DNS记录**!\n\n` +
      `此操作无法撤销。您确定要继续吗？`;

    const inlineKeyboard = {
      inline_keyboard: [
        [
          {
            text: '❌ Cancel',
            callback_data: 'cancel_delete_all'
          },
          {
            text: '🗑️ DELETE ALL',
            callback_data: 'confirm_delete_all'
          }
        ]
      ]
    };

    await sendMessage(c, chatId, confirmationMessage, inlineKeyboard);

  } catch (error) {
    console.error('Error preparing delete all DNS records:', error);
    await sendMessage(c, chatId,
      `❌ 准备删除失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

async function executeDeleteAllDNSRecords(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    // Show processing message
    await sendMessage(c, chatId, '🔄 正在删除所有DNS记录... ' + '这可能需要一些时间.');

    // Delete all records
    const result = await deleteAllUserDNSRecords(c, userId);

    // Log audit event
    await logAuditEvent(c, userId, 'DELETE_ALL_DNS_RECORDS', 'dns_records', 'all',
      `Deleted all DNS records: ${result.deleted} successful, ${result.errors.length} errors`);

    // Send result message
    let resultMessage = `✅ 删除完成!\n\n`;
    resultMessage += `🗑️ 成功删除: ${result.deleted} 条记录\n`;

    if (result.errors.length > 0) {
      resultMessage += `❌ 删除失败: ${result.errors.length} 条记录\n\n`;
      resultMessage += `错误信息:\n`;
      result.errors.slice(0, 5).forEach((error, index) => {
        resultMessage += `${index + 1}. ${error}\n`;
      });

      if (result.errors.length > 5) {
        resultMessage += `... 还有 ${result.errors.length - 5} 个错误`;
      }
    }

    await sendMessage(c, chatId, resultMessage);

  } catch (error) {
    console.error('Error executing delete all DNS records:', error);
    await sendMessage(c, chatId,
      `❌ 删除记录失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

async function handleRenewExpiry(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    // Import DNS functions
    const { renewDNSExpiry, checkDNSExpiry } = await import('../services/dns');

    // Check current expiry status
    const currentStatus = await checkDNSExpiry(c);

    // Renew expiry time
    const newExpiryTime = await renewDNSExpiry(c);

    // Log audit event
    await logAuditEvent(c, userId, 'RENEW_DNS_EXPIRY', 'dns_expiry', 'renewal',
      `Renewed DNS expiry from ${currentStatus.expiryTime} to ${newExpiryTime}`);

    const renewalMessage = `✅ DNS 过期时间已续期！\n\n` +
      `🕐 新的过期时间: ${new Date(newExpiryTime).toLocaleString('zh-CN')}\n` +
      `⏰ 有效期: 24小时\n\n` +
      `💡 提示: 使用 /expiry 命令查看剩余时间`;

    await sendMessage(c, chatId, renewalMessage);

  } catch (error) {
    console.error('Error renewing DNS expiry:', error);
    await sendMessage(c, chatId,
      `❌ 续期失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

async function handleCheckExpiry(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    // Import DNS functions
    const { checkDNSExpiry } = await import('../services/dns');

    // Check expiry status
    const expiryStatus = await checkDNSExpiry(c);

    let statusMessage: string;

    if (!expiryStatus.expiryTime) {
      statusMessage = `⚠️ DNS 过期时间未设置\n\n` +
        `请使用 /renew 命令设置过期时间`;
    } else if (expiryStatus.isExpired) {
      statusMessage = `🚨 DNS 记录已过期！\n\n` +
        `📅 过期时间: ${new Date(expiryStatus.expiryTime).toLocaleString('zh-CN')}\n` +
        `⚠️ 所有DNS记录将在下次定时任务中被删除\n\n` +
        `🔄 使用 /renew 命令立即续期`;
    } else {
      statusMessage = `✅ DNS 记录有效\n\n` +
        `📅 过期时间: ${new Date(expiryStatus.expiryTime).toLocaleString('zh-CN')}\n` +
        `⏰ 剩余时间: ${expiryStatus.timeRemaining}\n\n` +
        `🔄 使用 /renew 命令续期 24 小时`;
    }

    await sendMessage(c, chatId, statusMessage);

  } catch (error) {
    console.error('Error checking DNS expiry:', error);
    await sendMessage(c, chatId,
      `❌ 查询失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

async function handleStatusCommand(c: Context<{ Bindings: Env }>, chatId: number) {
  try {
    // Import DNS functions
    const { checkDNSExpiry } = await import('../services/dns');

    // Check expiry status
    const expiryStatus = await checkDNSExpiry(c);

    let expiryInfo = '';
    if (expiryStatus.expiryTime) {
      if (expiryStatus.isExpired) {
        expiryInfo = `🚨 DNS已过期: ${new Date(expiryStatus.expiryTime).toLocaleString('zh-CN')}`;
      } else {
        expiryInfo = `✅ DNS有效期: ${expiryStatus.timeRemaining}`;
      }
    } else {
      expiryInfo = `⚠️ DNS过期时间未设置`;
    }

    const status = `🤖 DNS Bot 状态\n\n` +
      `✅ DNS Bot 在线\n` +
      `⏰ 时间: ${new Date().toLocaleString('zh-CN')}\n` +
      `🔧 版本: 1.0.0\n\n` +
      `📋 DNS状态:\n${expiryInfo}`;

    await sendMessage(c, chatId, status);
  } catch (error) {
    console.error('Error getting status:', error);
    const status = `🤖 DNS Bot 状态\n\n` +
      `✅ DNS Bot 在线\n` +
      `⏰ 时间: ${new Date().toLocaleString('zh-CN')}\n` +
      `🔧 版本: 1.0.0\n\n` +
      `❌ DNS状态查询失败`;

    await sendMessage(c, chatId, status);
  }
}

async function handleReminderCommand(c: Context<{ Bindings: Env }>, chatId: number, userId: number, args: string[]) {
  try {
    const subCommand = args[0]?.toLowerCase();

    switch (subCommand) {
      case 'status':
        await handleReminderStatus(c, chatId, userId);
        break;

      case 'test':
        await handleTestReminder(c, chatId, userId);
        break;

      case 'enable':
        await handleToggleReminder(c, chatId, userId, true);
        break;

      case 'disable':
        await handleToggleReminder(c, chatId, userId, false);
        break;

      default:
        await handleReminderHelp(c, chatId);
        break;
    }

  } catch (error) {
    console.error('Error handling reminder command:', error);
    await sendMessage(c, chatId,
      `❌ 提醒命令处理失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

async function handleReminderHelp(c: Context<{ Bindings: Env }>, chatId: number) {
  const helpMessage = `🔔 DNS过期提醒管理\n\n` +
    `📋 可用命令:\n` +
    `• /reminder status - 查看提醒状态\n` +
    `• /reminder test - 发送测试提醒\n` +
    `• /reminder enable - 启用提醒\n` +
    `• /reminder disable - 禁用提醒\n\n` +
    `ℹ️ 提醒说明:\n` +
    `• 在DNS过期前1小时开始提醒\n` +
    `• 每5分钟提醒一次\n` +
    `• 自动发送给所有活跃用户`;

  await sendMessage(c, chatId, helpMessage);
}

async function handleReminderStatus(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    const { getReminderStatus } = await import('../services/reminder');
    const { checkDNSExpiry } = await import('../services/dns');

    const reminderStatus = await getReminderStatus(c);
    const expiryStatus = await checkDNSExpiry(c);

    let statusMessage = `🔔 提醒状态\n\n`;

    statusMessage += `📊 提醒设置:\n`;
    statusMessage += `• 状态: ${reminderStatus.enabled ? '✅ 已启用' : '❌ 已禁用'}\n`;

    if (reminderStatus.lastReminderTime) {
      statusMessage += `• 上次提醒: ${new Date(reminderStatus.lastReminderTime).toLocaleString('zh-CN')}\n`;
    } else {
      statusMessage += `• 上次提醒: 从未发送\n`;
    }

    if (reminderStatus.nextCheckIn) {
      statusMessage += `• 下次检查: ${reminderStatus.nextCheckIn}\n`;
    }

    statusMessage += `\n📅 DNS状态:\n`;
    if (expiryStatus.expiryTime) {
      if (expiryStatus.isExpired) {
        statusMessage += `• 状态: 🚨 已过期\n`;
      } else {
        statusMessage += `• 状态: ✅ 有效\n`;
        statusMessage += `• 剩余时间: ${expiryStatus.timeRemaining}\n`;
      }
      statusMessage += `• 过期时间: ${new Date(expiryStatus.expiryTime).toLocaleString('zh-CN')}\n`;
    } else {
      statusMessage += `• 状态: ⚠️ 未设置过期时间\n`;
    }

    await sendMessage(c, chatId, statusMessage);

  } catch (error) {
    console.error('Error getting reminder status:', error);
    await sendMessage(c, chatId,
      `❌ 获取提醒状态失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

async function handleTestReminder(c: Context<{ Bindings: Env }>, chatId: number, userId: number) {
  try {
    const { sendTestReminder } = await import('../services/reminder');

    await sendMessage(c, chatId, '🧪 正在发送测试提醒...');

    const success = await sendTestReminder(c, chatId);

    if (success) {
      await logAuditEvent(c, userId, 'TEST_REMINDER', 'reminder', 'test',
        `User ${userId} sent test reminder to chat ${chatId}`);
    } else {
      await sendMessage(c, chatId, '❌ 测试提醒发送失败');
    }

  } catch (error) {
    console.error('Error sending test reminder:', error);
    await sendMessage(c, chatId,
      `❌ 测试提醒失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

async function handleToggleReminder(c: Context<{ Bindings: Env }>, chatId: number, userId: number, enabled: boolean) {
  try {
    const { toggleReminders } = await import('../services/reminder');

    await toggleReminders(c, enabled);

    const statusText = enabled ? '启用' : '禁用';
    const statusEmoji = enabled ? '✅' : '❌';

    await logAuditEvent(c, userId, 'TOGGLE_REMINDER', 'reminder', 'toggle',
      `User ${userId} ${statusText} reminders`);

    await sendMessage(c, chatId,
      `${statusEmoji} 提醒功能已${statusText}\n\n` +
      `${enabled ?
        '🔔 现在会在DNS过期前1小时开始提醒\n每5分钟提醒一次' :
        '🔕 不再发送过期提醒'}`
    );

  } catch (error) {
    console.error('Error toggling reminders:', error);
    await sendMessage(c, chatId,
      `❌ 设置提醒失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

async function handleTextMessage(c: Context<{ Bindings: Env }>, chatId: number, userId: number, text: string) {
  // For now, just show help for non-command messages
  await sendMessage(c, chatId, '请使用命令与机器人交互。输入 /help 查看可用命令。');
}

async function handleCallbackQuery(c: Context<{ Bindings: Env }>, callbackQuery: any) {
  // Handle inline keyboard callbacks
  let chatId = callbackQuery.message?.chat?.id;
  const userId = callbackQuery.from.id;
  const data = callbackQuery.data;

  console.log('Callback query received:', {
    chatId,
    userId,
    data,
    hasMessage: !!callbackQuery.message,
    hasChat: !!callbackQuery.message?.chat,
    messageType: typeof callbackQuery.message
  });

  // If chatId is not available from message, try to get it from database
  if (!chatId && userId) {
    console.log('ChatId not found in callback, looking up in database for userId:', userId);
    try {
      const userRecord = await c.env.DB.prepare(
        'SELECT chat_id FROM users WHERE id = ?'
      ).bind(userId).first();

      if (userRecord?.chat_id) {
        chatId = userRecord.chat_id as number;
        console.log('Found chatId in database:', chatId);
      }
    } catch (error) {
      console.error('Error looking up chatId:', error);
    }
  }

  if (!chatId || !data) {
    console.log('Missing chatId or data after lookup:', { chatId, data, userId });
    return;
  }

  // Answer the callback query
  await fetch(`https://api.telegram.org/bot${c.env.TELEGRAM_BOT_TOKEN}/answerCallbackQuery`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      callback_query_id: callbackQuery.id,
      text: 'Processing...',
    }),
  });

  // Get user role for permission checking
  const userRole = await getUserRole(c, userId);

  // Check callback permission using configuration
  const permissionCheck = PermissionUtils.hasCallbackPermission(userRole, data);

  if (!permissionCheck.allowed) {
    if (permissionCheck.requiredRole) {
      const roleNames = {
        [UserRole.USER]: '普通用户',
        [UserRole.ADMIN]: '管理员',
        [UserRole.SUPER_ADMIN]: '超级管理员'
      };
      await sendMessage(c, chatId,
        `❌ 您没有权限执行此操作。\n` +
        `所需角色: ${roleNames[permissionCheck.requiredRole]}\n` +
        `您的角色: ${roleNames[userRole]}`
      );
    } else {
      console.log('Unknown callback data:', data);
    }
    return;
  }

  // Handle different callback data (permission already verified)
  if (data.startsWith('delete_')) {
    console.log('Handling delete record callback');
    const recordId = data.replace('delete_', '');
    await handleDeleteDNSRecord(c, chatId, userId, recordId);
  } else if (data === 'confirm_delete_all') {
    console.log('Handling confirm delete all callback');
    await executeDeleteAllDNSRecords(c, chatId, userId);
  } else if (data === 'cancel_delete_all') {
    console.log('Handling cancel delete all callback');
    await sendMessage(c, chatId, '✅ 删除已取消。您的DNS记录是安全的。');
  } else if (data.startsWith('setrole_')) {
    console.log('Handling set role callback');
    await handleSetRoleCallback(c, chatId, userId, data);
  } else if (data === 'refresh_users') {
    console.log('Handling refresh users callback');
    await handleUsersCommand(c, chatId, userId);
  } else if (data.startsWith('userinfo_')) {
    console.log('Handling user info callback');
    const targetUserId = data.replace('userinfo_', '');
    await handleUserInfoCallback(c, chatId, userId, targetUserId);
  } else {
    console.log('Unknown callback data:', data);
  }
}

async function storeUserInfo(c: Context<{ Bindings: Env }>, user: any, chatId?: number) {
  try {
    // First, try to update existing user without changing role
    const updateResult = await c.env.DB.prepare(`
      UPDATE users
      SET username = ?, first_name = ?, last_name = ?, chat_id = ?, last_active = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(
      user.username || null,
      user.first_name,
      user.last_name || null,
      chatId || null,
      user.id
    ).run();

    // If no rows were updated, the user doesn't exist, so insert new user
    if (updateResult.meta.changes === 0) {
      await c.env.DB.prepare(`
        INSERT INTO users (id, username, first_name, last_name, chat_id, role, last_active)
        VALUES (?, ?, ?, ?, ?, 'user', CURRENT_TIMESTAMP)
      `).bind(
        user.id,
        user.username || null,
        user.first_name,
        user.last_name || null,
        chatId || null
      ).run();
    }
  } catch (error) {
    console.error('Error storing user info:', error);
  }
}

// Handle set role callback from inline button
async function handleSetRoleCallback(c: Context<{ Bindings: Env }>, chatId: number, userId: number, data: string) {
  try {
    // Permission already checked in handleCallbackQuery

    // Parse callback data: setrole_[target_user_id]_[new_role]
    // Handle super_admin role which contains underscore
    const match = data.match(/^setrole_(\d+)_(.+)$/);
    if (!match) {
      await sendMessage(c, chatId, '❌ 无效的回调数据格式。');
      return;
    }

    const targetUserId = parseInt(match[1]!);
    const newRole = match[2]!;

    if (isNaN(targetUserId)) {
      await sendMessage(c, chatId, '❌ 无效的用户ID。');
      return;
    }

    if (!Object.values(UserRole).includes(newRole as UserRole)) {
      await sendMessage(c, chatId, '❌ 无效的角色。');
      return;
    }

    // Get target user info for confirmation
    const targetUser = await c.env.DB.prepare(
      'SELECT id, username, first_name, role FROM users WHERE id = ?'
    ).bind(targetUserId).first();

    if (!targetUser) {
      await sendMessage(c, chatId, '❌ 目标用户未找到。');
      return;
    }

    // Update user role in database
    await c.env.DB.prepare(`
      UPDATE users SET role = ? WHERE id = ?
    `).bind(newRole, targetUserId).run();

    // Log the role change
    await logAuditEvent(c, userId, 'SET_USER_ROLE', 'user_management', 'role_change',
      `Set role for user ${targetUserId} to ${newRole} via button`);

    const targetName = targetUser.first_name || targetUser.username || '未知用户';
    const oldRole = targetUser.role || 'user';

    await sendMessage(c, chatId,
      `✅ 用户角色更新成功!\n\n` +
      `👤 用户: ${targetName} (${targetUserId})\n` +
      `🔄 变更: ${PermissionUtils.getRoleDisplayName(oldRole as UserRole)} → ${PermissionUtils.getRoleDisplayName(newRole as UserRole)}\n\n` +
      `该用户在下次交互时将拥有${PermissionUtils.getRoleDisplayName(newRole as UserRole)}权限。`
    );

    // Refresh the user list to show updated roles
    setTimeout(() => {
      handleUsersCommand(c, chatId, userId);
    }, 1000);

  } catch (error) {
    console.error('Error handling set role callback:', error);
    await sendMessage(c, chatId,
      `❌ 修改用户角色失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

// Handle user info callback from inline button
async function handleUserInfoCallback(c: Context<{ Bindings: Env }>, chatId: number, userId: number, targetUserId: string) {
  try {
    // Permission already checked in handleCallbackQuery

    const targetUserIdNum = parseInt(targetUserId);
    if (isNaN(targetUserIdNum)) {
      await sendMessage(c, chatId, '❌ 无效的用户ID。');
      return;
    }

    // Get detailed user info
    const user = await c.env.DB.prepare(`
      SELECT id, username, first_name, last_name, role, created_at, last_active
      FROM users WHERE id = ?
    `).bind(targetUserIdNum).first();

    if (!user) {
      await sendMessage(c, chatId, '❌ 用户未找到。');
      return;
    }

    const fullName = [user.first_name, user.last_name].filter(Boolean).join(' ') || '未提供';
    const username = user.username || '未设置';
    const role = (user.role as string) || 'user';
    const created = user.created_at ? TimeUtils.formatBeijingTime(user.created_at as string) : MESSAGES.TIME.UNKNOWN;
    const lastActive = user.last_active ? TimeUtils.formatBeijingTime(user.last_active as string) : MESSAGES.TIME.NEVER;

    const roleEmojiMap: Record<string, string> = {
      'super_admin': '👑',
      'admin': '👨‍💼',
      'user': '👤'
    };
    const roleEmoji = roleEmojiMap[role] || '👤';

    await sendMessage(c, chatId,
      `👤 用户详情\n\n` +
      `🆔 ID: ${user.id}\n` +
      `📝 姓名: ${fullName}\n` +
      `🏷️ 用户名: @${username}\n` +
      `${roleEmoji} 角色: ${PermissionUtils.getRoleDisplayName(role as UserRole)}\n` +
      `📅 创建时间: ${TimeUtils.formatBeijingTime(created)}\n` +
      `⏰ 最后活跃: ${TimeUtils.formatBeijingTime(lastActive)}\n\n` +
      `使用 /setrole ${user.id} [角色] 来更改其角色。`
    );

  } catch (error) {
    console.error('Error handling user info callback:', error);
    await sendMessage(c, chatId,
      `❌ 获取用户信息失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

async function sendMessage(c: Context<{ Bindings: Env }>, chatId: number, text: string, replyMarkup?: any) {
  const payload: any = {
    chat_id: chatId,
    text,
    // parse_mode: 'HTML', // Disabled to avoid HTML parsing issues
  };

  if (replyMarkup) {
    payload.reply_markup = replyMarkup;
  }

  const response = await fetch(`https://api.telegram.org/bot${c.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const error = await response.text();
    console.error('Failed to send message:', error);
    throw new Error(`Telegram API error: ${error}`);
  }

  return response.json();
}
