name = "dns-bot"
main = "dist/index.js"
compatibility_date = "2023-12-01"

# Environment variables
[vars]
# These will be set via wrangler secrets
TELEGRAM_BOT_TOKEN = "**********************************************"
CLOUDFLARE_API_TOKEN = "****************************************"
CLOUDFLARE_ZONE_ID = "98e38c8d191d3484244907165d8d2ab6"

# D1 Database binding
[[d1_databases]]
binding = "DB"
database_name = "dns-bot-db"
database_id = "b5accee7-1828-40a6-85a3-54fd6343c104"

# Cron triggers for scheduled tasks
[triggers]
crons = [
  "0 0 * * *",      # Daily cleanup at midnight UTC
  "*/5 * * * *"     # Reminder check every 5 minutes
]

# KV namespace for caching (optional) - commented out for now
# [[kv_namespaces]]
# binding = "CACHE"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"

[build]
command = "npm run build:quick"

# Development configuration
[env.development]
vars = { ENVIRONMENT = "development" }

# Production configuration  
[env.production]
vars = { ENVIRONMENT = "production" }

# wrangler.toml (wrangler v3.88.0^)
[observability.logs]
enabled = true



