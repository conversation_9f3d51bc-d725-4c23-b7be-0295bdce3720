-- Remove foreign key constraints by recreating backup tables only
-- Keep existing tables intact

-- Drop existing backup tables if they exist
DROP TABLE IF EXISTS dns_backup_records;
DROP TABLE IF EXISTS dns_backups;

-- Recreate DNS backups table without foreign keys
CREATE TABLE dns_backups (
  backup_id TEXT PRIMARY KEY,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by INTEGER NOT NULL,
  reason TEXT NOT NULL,
  record_count INTEGER NOT NULL DEFAULT 0,
  zone_id TEXT NOT NULL,
  zone_name TEXT NOT NULL,
  backup_type TEXT NOT NULL DEFAULT 'manual',
  metadata TEXT
);

-- Recreate DNS backup records table without foreign keys
CREATE TABLE dns_backup_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  backup_id TEXT NOT NULL,
  record_id TEXT NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  content TEXT NOT NULL,
  proxied INTEGER NOT NULL DEFAULT 0,
  ttl INTEGER NOT NULL DEFAULT 1,
  zone_id TEXT NOT NULL,
  zone_name TEXT NOT NULL,
  created_on DATETIME,
  modified_on DATETIME,
  record_data TEXT
);

-- Create indexes for backup tables
CREATE INDEX IF NOT EXISTS idx_dns_backups_created_at ON dns_backups(created_at);
CREATE INDEX IF NOT EXISTS idx_dns_backups_created_by ON dns_backups(created_by);
CREATE INDEX IF NOT EXISTS idx_dns_backups_type ON dns_backups(backup_type);
CREATE INDEX IF NOT EXISTS idx_dns_backup_records_backup_id ON dns_backup_records(backup_id);
CREATE INDEX IF NOT EXISTS idx_dns_backup_records_name ON dns_backup_records(name);
CREATE INDEX IF NOT EXISTS idx_dns_backup_records_type ON dns_backup_records(type);
